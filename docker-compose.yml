version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: agentico
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Local File Storage (replacing MinIO)
  # Files are stored in Docker volumes and served via backend

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/agentico
      - REDIS_URL=redis://redis:6379
      - FILE_STORAGE_TYPE=local
      - FILE_STORAGE_PATH=/app/storage
      - FILE_UPLOAD_PATH=/app/uploads
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - ENVIRONMENT=development
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
    volumes:
      - ./backend:/app
      - file_storage:/app/storage
      - file_uploads:/app/uploads
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-super-secret-nextauth-key-change-in-production
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=********************************************/agentico
      - REDIS_URL=redis://redis:6379
      - FILE_STORAGE_TYPE=local
      - FILE_STORAGE_PATH=/app/storage
      - FILE_UPLOAD_PATH=/app/uploads
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - ENVIRONMENT=development
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
    volumes:
      - ./backend:/app
      - file_storage:/app/storage
      - file_uploads:/app/uploads
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=********************************************/agentico
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  file_storage:
  file_uploads:
