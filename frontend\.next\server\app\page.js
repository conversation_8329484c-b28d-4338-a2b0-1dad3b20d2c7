(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9491:e=>{"use strict";e.exports=require("assert")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1017:e=>{"use strict";e.exports=require("path")},2781:e=>{"use strict";e.exports=require("stream")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},2491:()=>{},9397:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=r(1355),s=r(862),o=r(5745),n=r.n(o),i=r(4635),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8693)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2808)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,6608,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\page.tsx"],u="/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5654:(e,t,r)=>{Promise.resolve().then(r.bind(r,3223))},8828:(e,t,r)=>{Promise.resolve().then(r.bind(r,5503))},272:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3907,23)),Promise.resolve().then(r.t.bind(r,7648,23)),Promise.resolve().then(r.t.bind(r,1944,23)),Promise.resolve().then(r.t.bind(r,9297,23)),Promise.resolve().then(r.t.bind(r,2649,23)),Promise.resolve().then(r.t.bind(r,4423,23))},3223:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(5452);r(2339);var s=r(692),o=r(4959),n=r(2652);function i(){return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[a.jsx("header",{className:"relative z-10",children:a.jsx("nav",{className:"mx-auto max-w-7xl px-6 lg:px-8","aria-label":"Top",children:(0,a.jsxs)("div",{className:"flex w-full items-center justify-between border-b border-blue-500/10 py-6",children:[a.jsx("div",{className:"flex items-center",children:(0,a.jsxs)(n.default,{href:"/",className:"flex items-center space-x-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CloudIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8 text-blue-600"}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Agentico"})]})}),a.jsx("div",{className:"ml-10 space-x-4",children:a.jsx(n.default,{href:"/auth/login",className:"inline-block rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 transition-colors",children:"Sign In"})})]})})}),(0,a.jsxs)("main",{children:[a.jsx("div",{className:"relative px-6 lg:px-8",children:a.jsx("div",{className:"mx-auto max-w-3xl pt-20 pb-32 sm:pt-48 sm:pb-40",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"hidden sm:mb-8 sm:flex sm:justify-center",children:(0,a.jsxs)("div",{className:"relative rounded-full px-3 py-1 text-sm leading-6 text-gray-500 ring-1 ring-gray-900/10 hover:ring-gray-900/20",children:["Next-generation AI agent platform."," ",(0,a.jsxs)(n.default,{href:"/docs",className:"font-semibold text-blue-600",children:[a.jsx("span",{className:"absolute inset-0","aria-hidden":"true"}),"Learn more ",a.jsx("span",{"aria-hidden":"true",children:"→"})]})]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:["AI Agents for"," ",a.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:"Enhanced Productivity"})]}),a.jsx("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Build, deploy, and manage intelligent AI agents that automate complex tasks, collaborate seamlessly, and adapt to your workflow. Experience the future of human-AI collaboration."}),(0,a.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[a.jsx(n.default,{href:"/auth/register",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors",children:"Get Started"}),(0,a.jsxs)(n.default,{href:"/demo",className:"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:["View Demo ",a.jsx("span",{"aria-hidden":"true",children:"→"})]})]})]})]})})}),(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[a.jsx("h2",{className:"text-base font-semibold leading-7 text-blue-600",children:"Powerful Features"}),a.jsx("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Everything you need to build intelligent agents"}),a.jsx("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Our platform provides all the tools and infrastructure needed to create, deploy, and manage AI agents at scale."})]}),a.jsx("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,a.jsxs)("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[a.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CloudIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Multi-Agent Architecture"]}),a.jsx("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:a.jsx("p",{className:"flex-auto",children:"Deploy specialized agents that work together to solve complex problems. Each agent can have unique capabilities and collaborate seamlessly."})})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[a.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CloudIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Visual Workflow Management"]}),a.jsx("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:a.jsx("p",{className:"flex-auto",children:"Design and manage complex workflows with our intuitive visual editor. Monitor execution in real-time and optimize performance."})})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[a.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CloudIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Enterprise Security"]}),a.jsx("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:a.jsx("p",{className:"flex-auto",children:"Built with enterprise-grade security, including isolated execution environments, role-based access control, and comprehensive audit logs."})})]})]})})]}),a.jsx("div",{className:"mx-auto mt-32 max-w-7xl px-6 sm:mt-40 lg:px-8",children:(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[a.jsx("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Ready to get started?"}),a.jsx("p",{className:"mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-600",children:"Join thousands of teams already using Agentico to automate their workflows and boost productivity."}),(0,a.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[a.jsx(n.default,{href:"/auth/register",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors",children:"Start Free Trial"}),(0,a.jsxs)(n.default,{href:"/contact",className:"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:["Contact Sales ",a.jsx("span",{"aria-hidden":"true",children:"→"})]})]})]})})]}),a.jsx("footer",{className:"mx-auto mt-32 max-w-7xl px-6 lg:px-8",children:a.jsx("div",{className:"border-t border-gray-900/10 py-16 sm:py-24 lg:py-32",children:a.jsx("div",{className:"text-center",children:a.jsx("p",{className:"text-sm leading-6 text-gray-600",children:"\xa9 2024 Agentico. All rights reserved."})})})})]})}!function(){var e=Error("Cannot find module '__barrel_optimize__?names=CloudIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();var l=r(3757);function c(){(0,s.useRouter)();let{user:e,isLoading:t,checkAuth:r}=(0,o.t)();return t||e?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(l.T,{size:"lg"})}):a.jsx(i,{})}},5503:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>n});var a=r(5452);(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}();var s=r(8268),o=r(2339);function n({children:e}){let[t]=(0,o.useState)(()=>Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}()),{client:t,children:[a.jsx(s.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),a.jsx(Object(function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}()),{initialIsOpen:!1})]})}},3757:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(5452),s=r(8219);let o={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function n({size:e="md",className:t}){return a.jsx("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",o[e],t)})}},200:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var a=r(5173);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let s=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class o{constructor(){this.client=a.Z.create({baseURL:`${s}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);r?.token&&(e.headers.Authorization=`Bearer ${r.token}`)}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:r}=JSON.parse(e);if(r?.refreshToken){let{access_token:e,refresh_token:a}=(await this.client.post("/auth/refresh",{refresh_token:r.refreshToken})).data,s={...r,token:e,refreshToken:a};return localStorage.setItem("auth-storage",JSON.stringify({state:s,version:0})),t.headers.Authorization=`Bearer ${e}`,this.client(t)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Bad request");break;case 401:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Authentication required");break;case 403:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Access denied");break;case 404:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Resource not found");break;case 422:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Validation error");break;case 429:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Too many requests. Please try again later.");break;case 500:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Server error. Please try again later.");break;default:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"An error occurred")}}else e.request?Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Network error. Please check your connection."):Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let a=new FormData;return a.append("file",t),this.client.post(e,a,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}}let n=new o},8219:(e,t,r)=>{"use strict";r.d(t,{SY:()=>n,cn:()=>o});var a=r(8229),s=r(3880);function o(...e){return(0,s.m6)((0,a.W)(e))}function n(e){let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/1e3);if(a<60)return"just now";let s=Math.floor(a/60);if(s<60)return`${s}m ago`;let o=Math.floor(s/60);if(o<24)return`${o}h ago`;let n=Math.floor(o/24);return n<7?`${n}d ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},4959:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var a=r(200);class s{async login(e){return(await a.x.post("/auth/login",e)).data}async register(e){return(await a.x.post("/auth/register",e)).data}async logout(){await a.x.post("/auth/logout")}async getCurrentUser(){return(await a.x.get("/auth/me")).data}async refreshToken(e){return(await a.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await a.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await a.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let o=new s;(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}();let n=Object(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e}())()(Object(function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}())((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await o.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await o.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await o.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await o.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:a}=t();a&&e({user:{...a,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))},2808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var a=r(1949),s=r(8898),o=r.n(s);r(98);var n=r(7873);let i=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx`),{__esModule:l,$$typeof:c}=i;i.default;let d=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let u={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function h({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:a.jsx("body",{className:o().className,children:(0,a.jsxs)(d,{children:[e,a.jsx(Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()),{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},8693:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>o,default:()=>i});var a=r(7873);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\page.tsx`),{__esModule:o,$$typeof:n}=s;s.default;let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\page.tsx#default`)},98:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[341,171],()=>r(9397));module.exports=a})();