"""
Task model
"""

from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Boolean, DateTime, Text, JSON, ForeignKey, Enum, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class TaskStatus(str, enum.Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(str, enum.Enum):
    """Task priority enumeration"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Task(Base):
    """Task model"""
    
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Task configuration
    task_type = Column(String(100), nullable=False)  # Type of task
    priority = Column(Enum(TaskPriority), default=TaskPriority.NORMAL, nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    
    # Input and output
    input_data = Column(JSON, default=dict, nullable=True)
    output_data = Column(JSON, default=dict, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Execution details
    celery_task_id = Column(String(255), nullable=True, index=True)  # Celery task ID
    execution_log = Column(JSON, default=list, nullable=True)  # Execution logs
    
    # Progress tracking
    progress_percentage = Column(Integer, default=0, nullable=False)
    current_step = Column(String(255), nullable=True)
    total_steps = Column(Integer, default=1, nullable=False)
    
    # Timing
    estimated_duration = Column(Integer, nullable=True)  # seconds
    actual_duration = Column(Float, nullable=True)  # seconds
    timeout_seconds = Column(Integer, default=300, nullable=False)
    
    # Retry configuration
    max_retries = Column(Integer, default=3, nullable=False)
    retry_count = Column(Integer, default=0, nullable=False)
    retry_delay = Column(Integer, default=60, nullable=False)  # seconds
    
    # Dependencies
    depends_on = Column(JSON, default=list, nullable=True)  # List of task IDs
    blocks = Column(JSON, default=list, nullable=True)  # List of task IDs this blocks
    
    # Metadata
    meta_data = Column(JSON, default=dict, nullable=True)
    tags = Column(JSON, default=list, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    scheduled_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="tasks")
    agent = relationship("Agent", back_populates="tasks")
    conversation = relationship("Conversation")
    
    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}', priority='{self.priority}')>"
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]
    
    @property
    def is_running(self) -> bool:
        """Check if task is running"""
        return self.status == TaskStatus.RUNNING
    
    @property
    def can_retry(self) -> bool:
        """Check if task can be retried"""
        return self.retry_count < self.max_retries and self.status == TaskStatus.FAILED
    
    def add_log_entry(self, level: str, message: str, data: dict = None):
        """Add log entry to execution log"""
        if not self.execution_log:
            self.execution_log = []
        
        log_entry = {
            "timestamp": func.now().isoformat(),
            "level": level,
            "message": message,
            "data": data or {}
        }
        self.execution_log.append(log_entry)
    
    def update_progress(self, percentage: int, current_step: str = None):
        """Update task progress"""
        self.progress_percentage = max(0, min(100, percentage))
        if current_step:
            self.current_step = current_step
    
    def mark_started(self):
        """Mark task as started"""
        self.status = TaskStatus.RUNNING
        self.started_at = func.now()
        self.add_log_entry("INFO", "Task started")
    
    def mark_completed(self, output_data: dict = None):
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = func.now()
        self.progress_percentage = 100
        if output_data:
            self.output_data = output_data
        
        # Calculate actual duration
        if self.started_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            self.actual_duration = duration
        
        self.add_log_entry("INFO", "Task completed successfully")
    
    def mark_failed(self, error_message: str):
        """Mark task as failed"""
        self.status = TaskStatus.FAILED
        self.completed_at = func.now()
        self.error_message = error_message
        
        # Calculate actual duration
        if self.started_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            self.actual_duration = duration
        
        self.add_log_entry("ERROR", f"Task failed: {error_message}")
    
    def increment_retry(self):
        """Increment retry count"""
        self.retry_count += 1
        self.status = TaskStatus.PENDING
        self.error_message = None
        self.add_log_entry("INFO", f"Task retry #{self.retry_count}")
    
    def to_dict(self) -> dict:
        """Convert task to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "task_type": self.task_type,
            "priority": self.priority.value if self.priority else None,
            "status": self.status.value if self.status else None,
            "input_data": self.input_data,
            "output_data": self.output_data,
            "error_message": self.error_message,
            "celery_task_id": self.celery_task_id,
            "execution_log": self.execution_log,
            "progress_percentage": self.progress_percentage,
            "current_step": self.current_step,
            "total_steps": self.total_steps,
            "estimated_duration": self.estimated_duration,
            "actual_duration": self.actual_duration,
            "timeout_seconds": self.timeout_seconds,
            "max_retries": self.max_retries,
            "retry_count": self.retry_count,
            "retry_delay": self.retry_delay,
            "depends_on": self.depends_on,
            "blocks": self.blocks,
            "metadata": self.meta_data,
            "tags": self.tags,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "user_id": self.user_id,
            "agent_id": self.agent_id,
            "conversation_id": self.conversation_id,
        }
