'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuthStore } from '@/stores/auth-store';
import { toast } from 'react-hot-toast';

interface UseWebSocketOptions {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  onMessage?: (event: string, data: any) => void;
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const { token } = useAuthStore();

  useEffect(() => {
    if (!token) return;

    const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
    
    // Create socket connection
    const socket = io(WS_URL, {
      auth: {
        token: token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    socketRef.current = socket;

    // Connection handlers
    socket.on('connect', () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setConnectionError(null);
      options.onConnect?.();
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setIsConnected(false);
      options.onDisconnect?.();
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionError(error.message);
      options.onError?.(error);
    });

    // Message handlers
    socket.on('connected', (data) => {
      console.log('WebSocket welcome message:', data);
      toast.success('Connected to Agentico');
    });

    socket.on('error', (data) => {
      console.error('WebSocket error:', data);
      toast.error(data.message || 'WebSocket error');
    });

    // Agent execution events
    socket.on('task_started', (data) => {
      console.log('Task started:', data);
      toast.success(`Agent task started`);
      options.onMessage?.('task_started', data);
    });

    socket.on('task_progress', (data) => {
      console.log('Task progress:', data);
      options.onMessage?.('task_progress', data);
    });

    socket.on('task_completed', (data) => {
      console.log('Task completed:', data);
      toast.success('Agent task completed successfully');
      options.onMessage?.('task_completed', data);
    });

    socket.on('task_failed', (data) => {
      console.log('Task failed:', data);
      toast.error(`Agent task failed: ${data.error}`);
      options.onMessage?.('task_failed', data);
    });

    // Conversation events
    socket.on('message_created', (data) => {
      console.log('Message created:', data);
      options.onMessage?.('message_created', data);
    });

    socket.on('agent_response', (data) => {
      console.log('Agent response:', data);
      options.onMessage?.('agent_response', data);
    });

    // Generic message handler
    socket.onAny((event, data) => {
      options.onMessage?.(event, data);
    });

    return () => {
      socket.disconnect();
      socketRef.current = null;
    };
  }, [token, options.onConnect, options.onDisconnect, options.onError, options.onMessage]);

  const emit = (event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  };

  const joinRoom = (room: string) => {
    emit('join_room', { room });
  };

  const leaveRoom = (room: string) => {
    emit('leave_room', { room });
  };

  const ping = () => {
    emit('ping', { timestamp: new Date().toISOString() });
  };

  return {
    isConnected,
    connectionError,
    emit,
    joinRoom,
    leaveRoom,
    ping,
    socket: socketRef.current,
  };
}
