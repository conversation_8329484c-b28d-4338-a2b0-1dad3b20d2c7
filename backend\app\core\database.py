"""
Database configuration and session management
"""

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from typing import Generator, AsyncGenerator
import asyncio

from app.core.config import settings

# Create sync engine
engine = create_engine(
    settings.database_url_sync,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

# Create async engine (lazy initialization)
async_engine = None

def get_async_engine():
    """Get or create async engine"""
    global async_engine
    if async_engine is None:
        async_engine = create_async_engine(
            settings.database_url_async,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=settings.DEBUG
        )
    return async_engine

# Create session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_async_session_local():
    """Get or create async session local"""
    engine = get_async_engine()
    return async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

# Create base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


def get_db():
    """Get database session (sync)"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    AsyncSessionLocal = get_async_session_local()
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def create_tables():
    """Create database tables"""
    engine = get_async_engine()
    async with engine.begin() as conn:
        # Import all models to ensure they are registered
        from app.models import user, agent, conversation, task, file

        # Create all tables
        await conn.run_sync(Base.metadata.create_all)


async def drop_tables():
    """Drop all database tables"""
    engine = get_async_engine()
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


async def reset_database():
    """Reset database (drop and create tables)"""
    await drop_tables()
    await create_tables()


# Database health check
async def check_database_health() -> bool:
    """Check if database is healthy"""
    try:
        engine = get_async_engine()
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        return True
    except Exception:
        return False


# Sync versions for compatibility
def create_tables_sync():
    """Create database tables (sync version)"""
    # Import all models to ensure they are registered
    from app.models import user, agent, conversation, task, file

    # Create all tables
    Base.metadata.create_all(bind=engine)


def check_database_health_sync() -> bool:
    """Check if database is healthy (sync version)"""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return True
    except Exception:
        return False
