"""
Conversation service for database operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func
from sqlalchemy.orm import selectinload

from app.models.conversation import Conversation, Message, ConversationStatus, MessageRole
from app.core.exceptions import NotFoundError, ValidationError


class ConversationService:
    """Conversation service for database operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, conversation_data: Dict[str, Any]) -> Conversation:
        """Create a new conversation"""
        conversation = Conversation(**conversation_data)
        self.db.add(conversation)
        await self.db.commit()
        await self.db.refresh(conversation)
        return conversation
    
    async def get_by_id(self, conversation_id: int) -> Optional[Conversation]:
        """Get conversation by ID with messages"""
        result = await self.db.execute(
            select(Conversation)
            .options(selectinload(Conversation.messages))
            .where(Conversation.id == conversation_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_user(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ConversationStatus] = None
    ) -> List[Conversation]:
        """Get conversations by user"""
        query = select(Conversation).where(Conversation.user_id == user_id)
        
        if status:
            query = query.where(Conversation.status == status)
        
        query = query.offset(skip).limit(limit).order_by(Conversation.updated_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_agent(
        self,
        agent_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Get conversations by agent"""
        query = select(Conversation).where(
            Conversation.agent_id == agent_id
        ).offset(skip).limit(limit).order_by(Conversation.updated_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update(self, conversation_id: int, conversation_data: Dict[str, Any]) -> Conversation:
        """Update conversation"""
        conversation = await self.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Update fields
        for field, value in conversation_data.items():
            if hasattr(conversation, field):
                setattr(conversation, field, value)
        
        conversation.updated_at = func.now()
        await self.db.commit()
        await self.db.refresh(conversation)
        return conversation
    
    async def delete(self, conversation_id: int) -> bool:
        """Delete conversation"""
        conversation = await self.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        await self.db.delete(conversation)
        await self.db.commit()
        return True
    
    async def add_message(
        self,
        conversation_id: int,
        role: MessageRole,
        content: str,
        metadata: Dict[str, Any] = None
    ) -> Message:
        """Add message to conversation"""
        conversation = await self.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Create message
        message = Message(
            conversation_id=conversation_id,
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        self.db.add(message)
        
        # Update conversation stats
        conversation.message_count += 1
        conversation.last_message_at = func.now()
        conversation.updated_at = func.now()
        
        await self.db.commit()
        await self.db.refresh(message)
        return message
    
    async def get_messages(
        self,
        conversation_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Message]:
        """Get messages for conversation"""
        query = select(Message).where(
            Message.conversation_id == conversation_id
        ).offset(skip).limit(limit).order_by(Message.created_at.asc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_recent_messages(
        self,
        conversation_id: int,
        limit: int = 10
    ) -> List[Message]:
        """Get recent messages for conversation"""
        query = select(Message).where(
            Message.conversation_id == conversation_id
        ).order_by(Message.created_at.desc()).limit(limit)
        
        result = await self.db.execute(query)
        messages = result.scalars().all()
        return list(reversed(messages))  # Return in chronological order
    
    async def search_conversations(
        self,
        search_term: str,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Search conversations by title or description"""
        search_pattern = f"%{search_term}%"
        
        query = select(Conversation).where(
            Conversation.user_id == user_id,
            (Conversation.title.ilike(search_pattern)) |
            (Conversation.description.ilike(search_pattern))
        ).offset(skip).limit(limit).order_by(Conversation.updated_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_conversation_stats(self, user_id: int) -> Dict[str, Any]:
        """Get conversation statistics for user"""
        # Get counts by status
        stats = {}
        for status in ConversationStatus:
            count = await self.db.scalar(
                select(func.count(Conversation.id)).where(
                    Conversation.user_id == user_id,
                    Conversation.status == status
                )
            )
            stats[f"{status.value}_count"] = count or 0
        
        # Get total count
        total_count = await self.db.scalar(
            select(func.count(Conversation.id)).where(Conversation.user_id == user_id)
        )
        stats["total_count"] = total_count or 0
        
        # Get total messages
        total_messages = await self.db.scalar(
            select(func.sum(Conversation.message_count)).where(Conversation.user_id == user_id)
        )
        stats["total_messages"] = total_messages or 0
        
        return stats
    
    async def archive_old_conversations(self, days: int = 30) -> int:
        """Archive conversations older than specified days"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        result = await self.db.execute(
            update(Conversation)
            .where(
                Conversation.updated_at < cutoff_date,
                Conversation.status == ConversationStatus.ACTIVE
            )
            .values(
                status=ConversationStatus.ARCHIVED,
                is_archived=True
            )
        )
        
        await self.db.commit()
        return result.rowcount
