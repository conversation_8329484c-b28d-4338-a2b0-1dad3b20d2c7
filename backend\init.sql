-- Initialize Agentico database
-- This file is executed when the PostgreSQL container starts

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE agentico'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'agentico')\gexec

-- Connect to the agentico database
\c agentico;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create indexes for better performance (will be created by SQLAlchemy migrations)
-- These are just placeholders for future optimizations
