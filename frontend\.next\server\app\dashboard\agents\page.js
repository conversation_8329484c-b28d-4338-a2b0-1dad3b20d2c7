(()=>{var e={};e.id=638,e.ids=[638],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9491:e=>{"use strict";e.exports=require("assert")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1017:e=>{"use strict";e.exports=require("path")},2781:e=>{"use strict";e.exports=require("stream")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},7915:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(1355),n=s(862),a=s(5745),i=s.n(a),o=s(4635),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["dashboard",{children:["agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3617)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\agents\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2808)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,6608,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\agents\\page.tsx"],u="/dashboard/agents/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/dashboard/agents/page",pathname:"/dashboard/agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4486:(e,t,s)=>{Promise.resolve().then(s.bind(s,5878))},5878:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(5452),n=s(2339);!function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}();var a=s(2652);!function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();var i=s(9927),o=s(3757),l=s(4491),c=s(8219);function d(){let[e,t]=(0,n.useState)(""),[s,d]=(0,n.useState)(""),{data:u,isLoading:x,error:p}=Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({queryKey:["agents",{search:e,type:s}],queryFn:()=>l.G.getAgents({agent_type:s||void 0})}),{data:m}=Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({queryKey:["agent-types"],queryFn:()=>l.G.getAgentTypes()}),g=u?.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description?.toLowerCase().includes(e.toLowerCase()))||[],h=e=>{switch(e){case"idle":return"bg-green-100 text-green-800";case"running":return"bg-blue-100 text-blue-800";case"error":return"bg-red-100 text-red-800";case"paused":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return r.jsx(i.c,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Agents"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your AI agents and their configurations"})]}),(0,r.jsxs)(a.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-4 w-4 mr-2"}),"Create Agent"]})]}),r.jsx("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"text",placeholder:"Search agents...",value:e,onChange:e=>t(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:s,onChange:e=>d(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[r.jsx("option",{value:"",children:"All Types"}),m?.types.map(e=>r.jsx("option",{value:e.value,children:e.name},e.value))]})]})}),x?r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx(o.T,{size:"lg"})}):p?r.jsx("div",{className:"text-center py-12",children:r.jsx("p",{className:"text-red-600",children:"Error loading agents"})}):0===g.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mx-auto h-12 w-12 text-gray-400"}),r.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No agents"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating your first agent."}),r.jsx("div",{className:"mt-6",children:(0,r.jsxs)(a.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-4 w-4 mr-2"}),"Create Agent"]})})]}):r.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:g.map(e=>r.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8 text-blue-500"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),r.jsx("p",{className:"text-sm text-gray-500",children:e.agent_type.replace("_"," ")})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${h(e.status)}`,children:e.status}),r.jsx("button",{className:"text-gray-400 hover:text-gray-600",children:r.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5"})})]})]}),r.jsx("div",{className:"mt-4",children:r.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description||"No description provided"})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[e.total_executions," executions"]}),r.jsx("span",{children:(0,c.SY)(e.updated_at)})]}),r.jsx("div",{className:"mt-4",children:r.jsx(a.default,{href:`/dashboard/agents/${e.id}`,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"View Details"})})]})},e.id))})]})})}},3617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});var r=s(7873);let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\agents\page.tsx`),{__esModule:a,$$typeof:i}=n;n.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\agents\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[341,171,517],()=>s(7915));module.exports=r})();