(()=>{var e={};e.id=638,e.ids=[638],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},9801:e=>{"use strict";e.exports=require("os")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},4175:e=>{"use strict";e.exports=require("tty")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},5406:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(3191),a=s(8716),n=s(7922),i=s.n(n),l=s(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["dashboard",{children:["agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6598)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\agents\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4968)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\agents\\page.tsx"],u="/dashboard/agents/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/agents/page",pathname:"/dashboard/agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1552:(e,t,s)=>{Promise.resolve().then(s.bind(s,5857))},5857:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(326),a=s(7577),n=s(108),i=s(434),l=s(4625);let o=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var d=s(8868);let c=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"}))});var u=s(7368),x=s(3955),m=s(6024),g=s(1223);function p(){let[e,t]=(0,a.useState)(""),[s,p]=(0,a.useState)(""),{data:h,isLoading:f,error:b}=(0,n.a)({queryKey:["agents",{search:e,type:s}],queryFn:()=>m.G.getAgents({agent_type:s||void 0})}),{data:y}=(0,n.a)({queryKey:["agent-types"],queryFn:()=>m.G.getAgentTypes()}),j=h?.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description?.toLowerCase().includes(e.toLowerCase()))||[],v=e=>{switch(e){case"idle":return"bg-green-100 text-green-800";case"running":return"bg-blue-100 text-blue-800";case"error":return"bg-red-100 text-red-800";case"paused":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return r.jsx(u.c,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Agents"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your AI agents and their configurations"})]}),(0,r.jsxs)(i.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[r.jsx(l.Z,{className:"h-4 w-4 mr-2"}),"Create Agent"]})]}),r.jsx("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"text",placeholder:"Search agents...",value:e,onChange:e=>t(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:s,onChange:e=>p(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[r.jsx("option",{value:"",children:"All Types"}),y?.types.map(e=>r.jsx("option",{value:e.value,children:e.name},e.value))]})]})}),f?r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx(x.T,{size:"lg"})}):b?r.jsx("div",{className:"text-center py-12",children:r.jsx("p",{className:"text-red-600",children:"Error loading agents"})}):0===j.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(d.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),r.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No agents"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating your first agent."}),r.jsx("div",{className:"mt-6",children:(0,r.jsxs)(i.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[r.jsx(l.Z,{className:"h-4 w-4 mr-2"}),"Create Agent"]})})]}):r.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:j.map(e=>r.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx(d.Z,{className:"h-8 w-8 text-blue-500"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),r.jsx("p",{className:"text-sm text-gray-500",children:e.agent_type.replace("_"," ")})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${v(e.status)}`,children:e.status}),r.jsx("button",{className:"text-gray-400 hover:text-gray-600",children:r.jsx(c,{className:"h-5 w-5"})})]})]}),r.jsx("div",{className:"mt-4",children:r.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description||"No description provided"})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[e.total_executions," executions"]}),r.jsx("span",{children:(0,g.SY)(e.updated_at)})]}),r.jsx("div",{className:"mt-4",children:r.jsx(i.default,{href:`/dashboard/agents/${e.id}`,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"View Details"})})]})},e.id))})]})})}},6598:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\agents\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[497,40,882,73],()=>s(5406));module.exports=r})();