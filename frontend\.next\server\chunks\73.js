exports.id=73,exports.ids=[73],exports.modules={510:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6563:(e,t,r)=>{Promise.resolve().then(r.bind(r,381)),Promise.resolve().then(r.bind(r,1012))},7368:(e,t,r)=>{"use strict";r.d(t,{c:()=>w});var s=r(326),a=r(7577),n=r(434),i=r(5047),o=r(982),l=r(8868),c=r(7710),d=r(5966),h=r(874),u=r(3351),m=r(2591),g=r(7477),x=r(882),f=r(4911),p=r(1223);let y=[{name:"Dashboard",href:"/dashboard",icon:o.Z},{name:"Agents",href:"/dashboard/agents",icon:l.Z},{name:"Conversations",href:"/dashboard/conversations",icon:c.Z},{name:"Files",href:"/dashboard/files",icon:d.Z},{name:"Settings",href:"/dashboard/settings",icon:h.Z}];function w({children:e}){let[t,r]=(0,a.useState)(!1),o=(0,i.usePathname)(),{user:l,logout:c}=(0,f.t)();return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:(0,p.cn)("fixed inset-0 z-50 lg:hidden",t?"block":"hidden"),children:[s.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>r(!1)}),(0,s.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,s.jsxs)(n.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[s.jsx(u.Z,{className:"h-8 w-8 text-blue-600"}),s.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]}),s.jsx("button",{onClick:()=>r(!1),className:"text-gray-400 hover:text-gray-600",children:s.jsx(m.Z,{className:"h-6 w-6"})})]}),s.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(e=>{let t=o===e.href;return(0,s.jsxs)(n.default,{href:e.href,className:(0,p.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>r(!1),children:[s.jsx(e.icon,{className:(0,p.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),s.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,s.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[s.jsx("div",{className:"flex h-16 items-center px-4",children:(0,s.jsxs)(n.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[s.jsx(u.Z,{className:"h-8 w-8 text-blue-600"}),s.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]})}),s.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(e=>{let t=o===e.href;return(0,s.jsxs)(n.default,{href:e.href,className:(0,p.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[s.jsx(e.icon,{className:(0,p.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})}),(0,s.jsxs)("div",{className:"lg:pl-64",children:[s.jsx("div",{className:"sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[s.jsx("button",{onClick:()=>r(!0),className:"text-gray-500 hover:text-gray-600 lg:hidden",children:s.jsx(g.Z,{className:"h-6 w-6"})}),s.jsx("div",{className:"flex items-center space-x-4",children:s.jsx("div",{className:"relative",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center",children:s.jsx(x.Z,{className:"h-5 w-5 text-white"})})}),(0,s.jsxs)("div",{className:"hidden md:block",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:l?.full_name||l?.username}),s.jsx("div",{className:"text-xs text-gray-500",children:l?.email})]}),s.jsx("button",{onClick:c,className:"text-sm text-gray-500 hover:text-gray-700",children:"Sign out"})]})})})]})}),s.jsx("main",{className:"flex-1",children:s.jsx("div",{className:"py-6",children:s.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})})]})]})}},1012:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>c});var s=r(326),a=r(3244),n=r(4976),i=r(787),o=r(3574),l=r(7577);function c({children:e}){let[t]=(0,l.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,s.jsxs)(n.aH,{client:t,children:[s.jsx(o.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),s.jsx(i.t,{initialIsOpen:!1})]})}},3955:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(326),a=r(1223);let n={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function i({size:e="md",className:t}){return s.jsx("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",n[e],t)})}},3002:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var s=r(4099),a=r(381);let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class i{constructor(){this.client=s.Z.create({baseURL:`${n}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);r?.token&&(e.headers.Authorization=`Bearer ${r.token}`)}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:r}=JSON.parse(e);if(r?.refreshToken){let{access_token:e,refresh_token:s}=(await this.client.post("/auth/refresh",{refresh_token:r.refreshToken})).data,a={...r,token:e,refreshToken:s};return localStorage.setItem("auth-storage",JSON.stringify({state:a,version:0})),t.headers.Authorization=`Bearer ${e}`,this.client(t)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:a.Am.error(r.detail||"Bad request");break;case 401:a.Am.error("Authentication required");break;case 403:a.Am.error("Access denied");break;case 404:a.Am.error("Resource not found");break;case 422:a.Am.error(r.detail||"Validation error");break;case 429:a.Am.error("Too many requests. Please try again later.");break;case 500:a.Am.error("Server error. Please try again later.");break;default:a.Am.error(r.detail||"An error occurred")}}else e.request?a.Am.error("Network error. Please check your connection."):a.Am.error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let s=new FormData;return s.append("file",t),this.client.post(e,s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}}let o=new i},1223:(e,t,r)=>{"use strict";r.d(t,{SY:()=>i,cn:()=>n});var s=r(1135),a=r(1009);function n(...e){return(0,a.m6)((0,s.W)(e))}function i(e){let t=new Date,r=new Date(e),s=Math.floor((t.getTime()-r.getTime())/1e3);if(s<60)return"just now";let a=Math.floor(s/60);if(a<60)return`${a}m ago`;let n=Math.floor(a/60);if(n<24)return`${n}h ago`;let i=Math.floor(n/24);return i<7?`${i}d ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},6024:(e,t,r)=>{"use strict";r.d(t,{G:()=>n});var s=r(3002);class a{async getAgents(e){return(await s.x.get("/agents",{params:e})).data}async getPublicAgents(e){return(await s.x.get("/agents/public",{params:e})).data}async getAgentTypes(){return(await s.x.get("/agents/types")).data}async getAgent(e){return(await s.x.get(`/agents/${e}`)).data}async createAgent(e){return(await s.x.post("/agents",e)).data}async updateAgent(e,t){return(await s.x.put(`/agents/${e}`,t)).data}async deleteAgent(e){await s.x.delete(`/agents/${e}`)}async getAgentStats(e){return(await s.x.get(`/agents/${e}/stats`)).data}async executeAgent(e,t){return(await s.x.post(`/agents/${e}/execute`,t)).data}async searchAgents(e,t){return(await s.x.get("/agents/search",{params:{q:e,...t}})).data}}let n=new a},4911:(e,t,r)=>{"use strict";r.d(t,{t:()=>l});var s=r(551),a=r(5251),n=r(3002);class i{async login(e){return(await n.x.post("/auth/login",e)).data}async register(e){return(await n.x.post("/auth/register",e)).data}async logout(){await n.x.post("/auth/logout")}async getCurrentUser(){return(await n.x.get("/auth/me")).data}async refreshToken(e){return(await n.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await n.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await n.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let o=new i,l=(0,s.Ue)()((0,a.tJ)((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await o.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await o.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await o.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await o.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:s}=t();s&&e({user:{...s,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))},4968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(9510),a=r(5384),n=r.n(a);r(5023);let i=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);var o=r(9125);let l={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function c({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:n().className,children:(0,s.jsxs)(i,{children:[e,s.jsx(o.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},5023:()=>{}};