import { apiClient } from '@/lib/api-client';
import type { Conversation, ConversationCreate, ConversationUpdate, Message, MessageCreate } from '@/types/conversation';

class ConversationService {
  async getConversations(params?: {
    skip?: number;
    limit?: number;
    status?: string;
  }): Promise<Conversation[]> {
    const response = await apiClient.get('/conversations', { params });
    return response.data;
  }

  async getConversation(id: number): Promise<Conversation> {
    const response = await apiClient.get(`/conversations/${id}`);
    return response.data;
  }

  async createConversation(data: ConversationCreate): Promise<Conversation> {
    const response = await apiClient.post('/conversations', data);
    return response.data;
  }

  async updateConversation(id: number, data: ConversationUpdate): Promise<Conversation> {
    const response = await apiClient.put(`/conversations/${id}`, data);
    return response.data;
  }

  async deleteConversation(id: number): Promise<void> {
    await apiClient.delete(`/conversations/${id}`);
  }

  async getMessages(conversationId: number, params?: {
    skip?: number;
    limit?: number;
  }): Promise<Message[]> {
    const response = await apiClient.get(`/conversations/${conversationId}/messages`, { params });
    return response.data;
  }

  async createMessage(conversationId: number, data: MessageCreate): Promise<Message> {
    const response = await apiClient.post(`/conversations/${conversationId}/messages`, data);
    return response.data;
  }

  async archiveConversation(id: number): Promise<void> {
    await apiClient.post(`/conversations/${id}/archive`);
  }

  async searchConversations(query: string, params?: {
    skip?: number;
    limit?: number;
  }): Promise<Conversation[]> {
    const response = await apiClient.get('/conversations/search', {
      params: { q: query, ...params }
    });
    return response.data;
  }
}

export const conversationService = new ConversationService();
