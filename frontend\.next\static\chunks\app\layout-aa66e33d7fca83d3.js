(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{5467:function(e,t,s){Promise.resolve().then(s.t.bind(s,4811,23)),Promise.resolve().then(s.bind(s,9064)),Promise.resolve().then(s.bind(s,2778)),Promise.resolve().then(s.bind(s,9554))},9554:function(e,t,s){"use strict";s.d(t,{Providers:function(){return $}});var i=s(7437),a=s(5345),r=s(1733),n=s(8238),o=s(4112),u=class extends o.l{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,s){let i=t.queryKey,n=t.queryHash??(0,a.Rm)(i,t),o=this.get(n);return o||(o=new r.A({client:e,queryKey:i,queryHash:n,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(i)}),this.add(o)),o}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){n.Vr.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,a._x)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,a._x)(e,t)):t}notify(e){n.Vr.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){n.Vr.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){n.Vr.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},l=s(7989),h=s(1255),c=class extends l.F{#t;#s;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.mutationCache,this.#t=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#a({type:"continue"})};this.#i=(0,h.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});let s="pending"===this.state.status,i=!this.#i.canStart();try{if(s)t();else{this.#a({type:"pending",variables:e,isPaused:i}),await this.#s.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:i})}let a=await this.#i.start();return await this.#s.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#s.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#a({type:"success",data:a}),a}catch(t){try{throw await this.#s.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#a({type:"error",error:t})}}finally{this.#s.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),n.Vr.batch(()=>{this.#t.forEach(t=>{t.onMutationUpdate(e)}),this.#s.notify({mutation:this,type:"updated",action:e})})}},d=class extends o.l{constructor(e={}){super(),this.config=e,this.#r=new Set,this.#n=new Map,this.#o=0}#r;#n;#o;build(e,t,s){let i=new c({mutationCache:this,mutationId:++this.#o,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#r.add(e);let t=m(e);if("string"==typeof t){let s=this.#n.get(t);s?s.push(e):this.#n.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#r.delete(e)){let t=m(e);if("string"==typeof t){let s=this.#n.get(t);if(s){if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#n.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=m(e);if("string"!=typeof t)return!0;{let s=this.#n.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=m(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#n.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){n.Vr.batch(()=>{this.#r.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#r.clear(),this.#n.clear()})}getAll(){return Array.from(this.#r)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,a.X7)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,a.X7)(e,t))}notify(e){n.Vr.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return n.Vr.batch(()=>Promise.all(e.map(e=>e.continue().catch(a.ZT))))}};function m(e){return e.options.scope?.id}var f=s(7045),y=s(7853);function p(e){return{onFetch:(t,s)=>{let i=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,a.cG)(t.options,t.fetchOptions),d=async(e,i,r)=>{if(s)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n={client:t.client,queryKey:t.queryKey,pageParam:i,direction:r?"backward":"forward",meta:t.options.meta};h(n);let o=await c(n),{maxPages:u}=t.options,l=r?a.Ht:a.VX;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,i,u)}};if(r&&n.length){let e="backward"===r,t={pages:n,pageParams:o},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:g)(i,t);u=await d(t,s,e)}else{let t=e??n.length;do{let e=0===l?o[0]??i.initialPageParam:g(i,u);if(l>0&&null==e)break;u=await d(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function g(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var v=class{#u;#s;#l;#h;#c;#d;#m;#f;constructor(e={}){this.#u=e.queryCache||new u,this.#s=e.mutationCache||new d,this.#l=e.defaultOptions||{},this.#h=new Map,this.#c=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#m=f.j.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onFocus())}),this.#f=y.N.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#m?.(),this.#m=void 0,this.#f?.(),this.#f=void 0)}isFetching(e){return this.#u.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#u.build(this,t),i=s.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,a.KC)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#u.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let i=this.defaultQueryOptions({queryKey:e}),r=this.#u.get(i.queryHash),n=r?.state.data,o=(0,a.SE)(t,n);if(void 0!==o)return this.#u.build(this,i).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return n.Vr.batch(()=>this.#u.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state}removeQueries(e){let t=this.#u;n.Vr.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#u;return n.Vr.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(n.Vr.batch(()=>this.#u.findAll(e).map(e=>e.cancel(s)))).then(a.ZT).catch(a.ZT)}invalidateQueries(e,t={}){return n.Vr.batch(()=>(this.#u.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(n.Vr.batch(()=>this.#u.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(a.ZT)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(a.ZT)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#u.build(this,t);return s.isStaleByTime((0,a.KC)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(a.ZT).catch(a.ZT)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(a.ZT).catch(a.ZT)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return y.N.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#u}getMutationCache(){return this.#s}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#h.set((0,a.Ym)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#h.values()],s={};return t.forEach(t=>{(0,a.to)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#c.set((0,a.Ym)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#c.values()],s={};return t.forEach(t=>{(0,a.to)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,a.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===a.CN&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#u.clear(),this.#s.clear()}},b=s(9827),C=function(){return null},w=s(2265);let O=["light","dark"],q="(prefers-color-scheme: dark)",P="undefined"==typeof window,S=(0,w.createContext)(void 0),E=e=>(0,w.useContext)(S)?w.createElement(w.Fragment,null,e.children):w.createElement(x,e),Q=["light","dark"],x=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:s=!0,enableColorScheme:i=!0,storageKey:a="theme",themes:r=Q,defaultTheme:n=s?"system":"light",attribute:o="data-theme",value:u,children:l,nonce:h})=>{let[c,d]=(0,w.useState)(()=>M(a,n)),[m,f]=(0,w.useState)(()=>M(a)),y=u?Object.values(u):r,p=(0,w.useCallback)(e=>{let a=e;if(!a)return;"system"===e&&s&&(a=D());let r=u?u[a]:a,l=t?A():null,h=document.documentElement;if("class"===o?(h.classList.remove(...y),r&&h.classList.add(r)):r?h.setAttribute(o,r):h.removeAttribute(o),i){let e=O.includes(n)?n:null,t=O.includes(a)?a:e;h.style.colorScheme=t}null==l||l()},[]),g=(0,w.useCallback)(e=>{d(e);try{localStorage.setItem(a,e)}catch(e){}},[e]),v=(0,w.useCallback)(t=>{f(D(t)),"system"===c&&s&&!e&&p("system")},[c,e]);(0,w.useEffect)(()=>{let e=window.matchMedia(q);return e.addListener(v),v(e),()=>e.removeListener(v)},[v]),(0,w.useEffect)(()=>{let e=e=>{e.key===a&&g(e.newValue||n)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[g]),(0,w.useEffect)(()=>{p(null!=e?e:c)},[e,c]);let b=(0,w.useMemo)(()=>({theme:c,setTheme:g,forcedTheme:e,resolvedTheme:"system"===c?m:c,themes:s?[...r,"system"]:r,systemTheme:s?m:void 0}),[c,g,e,m,s,r]);return w.createElement(S.Provider,{value:b},w.createElement(T,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:s,enableColorScheme:i,storageKey:a,themes:r,defaultTheme:n,attribute:o,value:u,children:l,attrs:y,nonce:h}),l)},T=(0,w.memo)(({forcedTheme:e,storageKey:t,attribute:s,enableSystem:i,enableColorScheme:a,defaultTheme:r,value:n,attrs:o,nonce:u})=>{let l="system"===r,h="class"===s?`var d=document.documentElement,c=d.classList;c.remove(${o.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${s}',s='setAttribute';`,c=a?O.includes(r)&&r?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${r}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(e,t=!1,i=!0)=>{let r=n?n[e]:e,o=t?e+"|| ''":`'${r}'`,u="";return a&&i&&!t&&O.includes(e)&&(u+=`d.style.colorScheme = '${e}';`),"class"===s?u+=t||r?`c.add(${o})`:"null":r&&(u+=`d[s](n,${o})`),u},m=e?`!function(){${h}${d(e)}}()`:i?`!function(){try{${h}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${l})){var t='${q}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${n?`var x=${JSON.stringify(n)};`:""}${d(n?"x[e]":"e",!0)}}${l?"":"else{"+d(r,!1,!1)+"}"}${c}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${t}');if(e){${n?`var x=${JSON.stringify(n)};`:""}${d(n?"x[e]":"e",!0)}}else{${d(r,!1,!1)};}${c}}catch(t){}}();`;return w.createElement("script",{nonce:u,dangerouslySetInnerHTML:{__html:m}})},()=>!0),M=(e,t)=>{let s;if(!P){try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t}},A=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},D=e=>(e||(e=window.matchMedia(q)),e.matches?"dark":"light");function $(e){let{children:t}=e,[s]=(0,w.useState)(()=>new v({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>{var s,i;return(!((null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.status)>=400)||!((null==t?void 0:null===(i=t.response)||void 0===i?void 0:i.status)<500))&&e<3}},mutations:{retry:!1}}}));return(0,i.jsxs)(b.aH,{client:s,children:[(0,i.jsx)(E,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:t}),(0,i.jsx)(C,{initialIsOpen:!1})]})}},2778:function(e,t,s){"use strict";s.r(t),t.default="ac2353319419"},4811:function(e){e.exports={style:{fontFamily:"'__Inter_d65c78', '__Inter_Fallback_d65c78'",fontStyle:"normal"},className:"__className_d65c78"}}},function(e){e.O(0,[2,64,763,971,117,744],function(){return e(e.s=5467)}),_N_E=e.O()}]);