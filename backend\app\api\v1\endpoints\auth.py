"""
Authentication endpoints
"""

from datetime import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, EmailStr

from app.core.database import get_async_db
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_password,
    get_password_hash,
    get_current_user
)
from app.core.config import settings
from app.core.exceptions import AuthenticationError, ValidationError
from app.models.user import User
from app.services.user_service import UserService

router = APIRouter()


class UserRegister(BaseModel):
    email: EmailStr
    username: str
    password: str
    full_name: str = None


class UserLogin(BaseModel):
    email: str
    password: str


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenRefresh(BaseModel):
    refresh_token: str


class PasswordReset(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


@router.post("/register", response_model=dict)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_async_db)
):
    """Register a new user"""
    user_service = UserService(db)
    
    # Check if user already exists
    existing_user = await user_service.get_by_email(user_data.email)
    if existing_user:
        raise ValidationError("Email already registered")
    
    existing_username = await user_service.get_by_username(user_data.username)
    if existing_username:
        raise ValidationError("Username already taken")
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    user = await user_service.create({
        "email": user_data.email,
        "username": user_data.username,
        "full_name": user_data.full_name,
        "hashed_password": hashed_password,
        "is_active": True,
        "is_verified": False
    })
    
    return {
        "message": "User registered successfully",
        "user": user.to_dict()
    }


@router.post("/login", response_model=Token)
async def login(
    user_data: UserLogin,
    db: AsyncSession = Depends(get_async_db)
):
    """Login user and return tokens"""
    user_service = UserService(db)
    
    # Get user by email
    user = await user_service.get_by_email(user_data.email)
    if not user:
        raise AuthenticationError("Invalid email or password")
    
    # Verify password
    if not verify_password(user_data.password, user.hashed_password):
        raise AuthenticationError("Invalid email or password")
    
    # Check if user is active
    if not user.is_active:
        raise AuthenticationError("Account is disabled")
    
    # Update last login
    await user_service.update_last_login(user.id)
    
    # Create tokens
    access_token = create_access_token(
        data={"sub": str(user.id), "email": user.email}
    )
    refresh_token = create_refresh_token(
        data={"sub": str(user.id), "email": user.email}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/login/form", response_model=Token)
async def login_form(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_async_db)
):
    """Login using OAuth2 form (for compatibility)"""
    user_data = UserLogin(email=form_data.username, password=form_data.password)
    return await login(user_data, db)


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_async_db)
):
    """Refresh access token"""
    try:
        # Verify refresh token
        payload = verify_token(token_data.refresh_token, token_type="refresh")
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("Invalid refresh token")
        
        # Get user
        user_service = UserService(db)
        user = await user_service.get_by_id(int(user_id))
        
        if not user or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new tokens
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        refresh_token = create_refresh_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    
    except Exception as e:
        raise AuthenticationError("Invalid refresh token")


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """Logout user (client should discard tokens)"""
    return {"message": "Logged out successfully"}


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return current_user.to_dict()


@router.post("/password-reset")
async def request_password_reset(
    reset_data: PasswordReset,
    db: AsyncSession = Depends(get_async_db)
):
    """Request password reset"""
    user_service = UserService(db)
    user = await user_service.get_by_email(reset_data.email)
    
    if user:
        # Generate reset token (in production, send via email)
        reset_token = create_access_token(
            data={"sub": str(user.id), "type": "password_reset"},
            expires_delta=timedelta(hours=1)
        )
        
        # In development, return token directly
        # In production, send via email and return success message
        if settings.ENVIRONMENT == "development":
            return {"message": "Password reset token generated", "token": reset_token}
        else:
            # TODO: Send email with reset link
            return {"message": "Password reset email sent"}
    
    # Always return success to prevent email enumeration
    return {"message": "If the email exists, a reset link has been sent"}


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_async_db)
):
    """Confirm password reset"""
    try:
        # Verify reset token
        payload = verify_token(reset_data.token)
        user_id = payload.get("sub")
        token_type = payload.get("type")
        
        if not user_id or token_type != "password_reset":
            raise AuthenticationError("Invalid reset token")
        
        # Get user and update password
        user_service = UserService(db)
        user = await user_service.get_by_id(int(user_id))
        
        if not user:
            raise AuthenticationError("User not found")
        
        # Update password
        hashed_password = get_password_hash(reset_data.new_password)
        await user_service.update(user.id, {"hashed_password": hashed_password})
        
        return {"message": "Password reset successfully"}
    
    except Exception as e:
        raise AuthenticationError("Invalid or expired reset token")
