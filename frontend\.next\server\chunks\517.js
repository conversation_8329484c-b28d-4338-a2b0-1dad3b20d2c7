exports.id=517,exports.ids=[517],exports.modules={2491:()=>{},8828:(e,t,r)=>{Promise.resolve().then(r.bind(r,5503))},272:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3907,23)),Promise.resolve().then(r.t.bind(r,7648,23)),Promise.resolve().then(r.t.bind(r,1944,23)),Promise.resolve().then(r.t.bind(r,9297,23)),Promise.resolve().then(r.t.bind(r,2649,23)),Promise.resolve().then(r.t.bind(r,4423,23))},9927:(e,t,r)=>{"use strict";r.d(t,{c:()=>d});var o=r(5452),n=r(2339),a=r(2652),s=r(692);!function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();var c=r(4959),i=r(8219);let l=[{name:"Dashboard",href:"/dashboard",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}())},{name:"Agents",href:"/dashboard/agents",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}())},{name:"Conversations",href:"/dashboard/conversations",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}())},{name:"Files",href:"/dashboard/files",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}())},{name:"Settings",href:"/dashboard/settings",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}())}];function d({children:e}){let[t,r]=(0,n.useState)(!1),d=(0,s.usePathname)(),{user:u,logout:h}=(0,c.t)();return(0,o.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,o.jsxs)("div",{className:(0,i.cn)("fixed inset-0 z-50 lg:hidden",t?"block":"hidden"),children:[o.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>r(!1)}),(0,o.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,o.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,o.jsxs)(a.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8 text-blue-600"}),o.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]}),o.jsx("button",{onClick:()=>r(!1),className:"text-gray-400 hover:text-gray-600",children:o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-6 w-6"})})]}),o.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:l.map(e=>{let t=d===e.href;return(0,o.jsxs)(a.default,{href:e.href,className:(0,i.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>r(!1),children:[o.jsx(e.icon,{className:(0,i.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),o.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,o.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[o.jsx("div",{className:"flex h-16 items-center px-4",children:(0,o.jsxs)(a.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8 text-blue-600"}),o.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]})}),o.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:l.map(e=>{let t=d===e.href;return(0,o.jsxs)(a.default,{href:e.href,className:(0,i.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[o.jsx(e.icon,{className:(0,i.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})}),(0,o.jsxs)("div",{className:"lg:pl-64",children:[o.jsx("div",{className:"sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200",children:(0,o.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[o.jsx("button",{onClick:()=>r(!0),className:"text-gray-500 hover:text-gray-600 lg:hidden",children:o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-6 w-6"})}),o.jsx("div",{className:"flex items-center space-x-4",children:o.jsx("div",{className:"relative",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center",children:o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=Bars3Icon,ChatBubbleLeftRightIcon,Cog6ToothIcon,CpuChipIcon,DocumentIcon,HomeIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 text-white"})})}),(0,o.jsxs)("div",{className:"hidden md:block",children:[o.jsx("div",{className:"text-sm font-medium text-gray-900",children:u?.full_name||u?.username}),o.jsx("div",{className:"text-xs text-gray-500",children:u?.email})]}),o.jsx("button",{onClick:h,className:"text-sm text-gray-500 hover:text-gray-700",children:"Sign out"})]})})})]})}),o.jsx("main",{className:"flex-1",children:o.jsx("div",{className:"py-6",children:o.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})})]})]})}},5503:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});var o=r(5452);(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(8268),a=r(2339);function s({children:e}){let[t]=(0,a.useState)(()=>Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}()),{client:t,children:[o.jsx(n.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),o.jsx(Object(function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}()),{initialIsOpen:!1})]})}},3757:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var o=r(5452),n=r(8219);let a={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function s({size:e="md",className:t}){return o.jsx("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",a[e],t)})}},200:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var o=r(5173);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class a{constructor(){this.client=o.Z.create({baseURL:`${n}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);r?.token&&(e.headers.Authorization=`Bearer ${r.token}`)}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:r}=JSON.parse(e);if(r?.refreshToken){let{access_token:e,refresh_token:o}=(await this.client.post("/auth/refresh",{refresh_token:r.refreshToken})).data,n={...r,token:e,refreshToken:o};return localStorage.setItem("auth-storage",JSON.stringify({state:n,version:0})),t.headers.Authorization=`Bearer ${e}`,this.client(t)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Bad request");break;case 401:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Authentication required");break;case 403:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Access denied");break;case 404:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Resource not found");break;case 422:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Validation error");break;case 429:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Too many requests. Please try again later.");break;case 500:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Server error. Please try again later.");break;default:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"An error occurred")}}else e.request?Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Network error. Please check your connection."):Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let o=new FormData;return o.append("file",t),this.client.post(e,o,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}}let s=new a},8219:(e,t,r)=>{"use strict";r.d(t,{SY:()=>s,cn:()=>a});var o=r(8229),n=r(3880);function a(...e){return(0,n.m6)((0,o.W)(e))}function s(e){let t=new Date,r=new Date(e),o=Math.floor((t.getTime()-r.getTime())/1e3);if(o<60)return"just now";let n=Math.floor(o/60);if(n<60)return`${n}m ago`;let a=Math.floor(n/60);if(a<24)return`${a}h ago`;let s=Math.floor(a/24);return s<7?`${s}d ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},4491:(e,t,r)=>{"use strict";r.d(t,{G:()=>a});var o=r(200);class n{async getAgents(e){return(await o.x.get("/agents",{params:e})).data}async getPublicAgents(e){return(await o.x.get("/agents/public",{params:e})).data}async getAgentTypes(){return(await o.x.get("/agents/types")).data}async getAgent(e){return(await o.x.get(`/agents/${e}`)).data}async createAgent(e){return(await o.x.post("/agents",e)).data}async updateAgent(e,t){return(await o.x.put(`/agents/${e}`,t)).data}async deleteAgent(e){await o.x.delete(`/agents/${e}`)}async getAgentStats(e){return(await o.x.get(`/agents/${e}/stats`)).data}async executeAgent(e,t){return(await o.x.post(`/agents/${e}/execute`,t)).data}async searchAgents(e,t){return(await o.x.get("/agents/search",{params:{q:e,...t}})).data}}let a=new n},4959:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var o=r(200);class n{async login(e){return(await o.x.post("/auth/login",e)).data}async register(e){return(await o.x.post("/auth/register",e)).data}async logout(){await o.x.post("/auth/logout")}async getCurrentUser(){return(await o.x.get("/auth/me")).data}async refreshToken(e){return(await o.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await o.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await o.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let a=new n;(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}();let s=Object(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e}())()(Object(function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}())((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await a.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await a.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await a.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await a.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:o}=t();o&&e({user:{...o,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))},2808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var o=r(1949),n=r(8898),a=r.n(n);r(98);var s=r(7873);let c=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx`),{__esModule:i,$$typeof:l}=c;c.default;let d=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let u={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function h({children:e}){return o.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:o.jsx("body",{className:a().className,children:(0,o.jsxs)(d,{children:[e,o.jsx(Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()),{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},98:()=>{}};