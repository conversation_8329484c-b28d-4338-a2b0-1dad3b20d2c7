(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{7204:function(e,t,r){Promise.resolve().then(r.bind(r,5122))},5122:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return x}});var a=r(7437),s=r(2265),n=r(9376),l=r(1770),i=r(7648),o=r(2087),c=r(1331);let d=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))});function u(){return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,a.jsx)("header",{className:"relative z-10",children:(0,a.jsx)("nav",{className:"mx-auto max-w-7xl px-6 lg:px-8","aria-label":"Top",children:(0,a.jsxs)("div",{className:"flex w-full items-center justify-between border-b border-blue-500/10 py-6",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(i.default,{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)(o.Z,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Agentico"})]})}),(0,a.jsx)("div",{className:"ml-10 space-x-4",children:(0,a.jsx)(i.default,{href:"/auth/login",className:"inline-block rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 transition-colors",children:"Sign In"})})]})})}),(0,a.jsxs)("main",{children:[(0,a.jsx)("div",{className:"relative px-6 lg:px-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-3xl pt-20 pb-32 sm:pt-48 sm:pb-40",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"hidden sm:mb-8 sm:flex sm:justify-center",children:(0,a.jsxs)("div",{className:"relative rounded-full px-3 py-1 text-sm leading-6 text-gray-500 ring-1 ring-gray-900/10 hover:ring-gray-900/20",children:["Next-generation AI agent platform."," ",(0,a.jsxs)(i.default,{href:"/docs",className:"font-semibold text-blue-600",children:[(0,a.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"Learn more ",(0,a.jsx)("span",{"aria-hidden":"true",children:"→"})]})]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:["AI Agents for"," ",(0,a.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:"Enhanced Productivity"})]}),(0,a.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Build, deploy, and manage intelligent AI agents that automate complex tasks, collaborate seamlessly, and adapt to your workflow. Experience the future of human-AI collaboration."}),(0,a.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,a.jsx)(i.default,{href:"/auth/register",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors",children:"Get Started"}),(0,a.jsxs)(i.default,{href:"/demo",className:"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:["View Demo ",(0,a.jsx)("span",{"aria-hidden":"true",children:"→"})]})]})]})]})})}),(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[(0,a.jsx)("h2",{className:"text-base font-semibold leading-7 text-blue-600",children:"Powerful Features"}),(0,a.jsx)("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Everything you need to build intelligent agents"}),(0,a.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Our platform provides all the tools and infrastructure needed to create, deploy, and manage AI agents at scale."})]}),(0,a.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,a.jsxs)("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[(0,a.jsx)(c.Z,{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Multi-Agent Architecture"]}),(0,a.jsx)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:(0,a.jsx)("p",{className:"flex-auto",children:"Deploy specialized agents that work together to solve complex problems. Each agent can have unique capabilities and collaborate seamlessly."})})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[(0,a.jsx)(d,{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Visual Workflow Management"]}),(0,a.jsx)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:(0,a.jsx)("p",{className:"flex-auto",children:"Design and manage complex workflows with our intuitive visual editor. Monitor execution in real-time and optimize performance."})})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("dt",{className:"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900",children:[(0,a.jsx)(o.Z,{className:"h-5 w-5 flex-none text-blue-600","aria-hidden":"true"}),"Enterprise Security"]}),(0,a.jsx)("dd",{className:"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600",children:(0,a.jsx)("p",{className:"flex-auto",children:"Built with enterprise-grade security, including isolated execution environments, role-based access control, and comprehensive audit logs."})})]})]})})]}),(0,a.jsx)("div",{className:"mx-auto mt-32 max-w-7xl px-6 sm:mt-40 lg:px-8",children:(0,a.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Ready to get started?"}),(0,a.jsx)("p",{className:"mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-600",children:"Join thousands of teams already using Agentico to automate their workflows and boost productivity."}),(0,a.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,a.jsx)(i.default,{href:"/auth/register",className:"rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors",children:"Start Free Trial"}),(0,a.jsxs)(i.default,{href:"/contact",className:"text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:["Contact Sales ",(0,a.jsx)("span",{"aria-hidden":"true",children:"→"})]})]})]})})]}),(0,a.jsx)("footer",{className:"mx-auto mt-32 max-w-7xl px-6 lg:px-8",children:(0,a.jsx)("div",{className:"border-t border-gray-900/10 py-16 sm:py-24 lg:py-32",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-sm leading-6 text-gray-600",children:"\xa9 2024 Agentico. All rights reserved."})})})})]})}var h=r(7692);function x(){let e=(0,n.useRouter)(),{user:t,isLoading:r,checkAuth:i}=(0,l.t)();return((0,s.useEffect)(()=>{i()},[i]),(0,s.useEffect)(()=>{t&&!r&&e.push("/dashboard")},[t,r,e]),r||t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(h.T,{size:"lg"})}):(0,a.jsx)(u,{})}},7692:function(e,t,r){"use strict";r.d(t,{T:function(){return l}});var a=r(7437),s=r(3448);let n={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function l(e){let{size:t="md",className:r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",n[t],r)})}},3227:function(e,t,r){"use strict";r.d(t,{x:function(){return i}});var a=r(3464),s=r(9064);let n=r(257).env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class l{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);(null==r?void 0:r.token)&&(e.headers.Authorization="Bearer ".concat(r.token))}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){r._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:t}=JSON.parse(e);if(null==t?void 0:t.refreshToken){let{access_token:e,refresh_token:a}=(await this.client.post("/auth/refresh",{refresh_token:t.refreshToken})).data,s={...t,token:e,refreshToken:a};return localStorage.setItem("auth-storage",JSON.stringify({state:s,version:0})),r.headers.Authorization="Bearer ".concat(e),this.client(r)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:s.Am.error(r.detail||"Bad request");break;case 401:s.Am.error("Authentication required");break;case 403:s.Am.error("Access denied");break;case 404:s.Am.error("Resource not found");break;case 422:s.Am.error(r.detail||"Validation error");break;case 429:s.Am.error("Too many requests. Please try again later.");break;case 500:s.Am.error("Server error. Please try again later.");break;default:s.Am.error(r.detail||"An error occurred")}}else e.request?s.Am.error("Network error. Please check your connection."):s.Am.error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let a=new FormData;return a.append("file",t),this.client.post(e,a,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}constructor(){this.client=a.Z.create({baseURL:"".concat(n,"/api/v1"),timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let i=new l},3448:function(e,t,r){"use strict";r.d(t,{SY:function(){return l},cn:function(){return n}});var a=r(1994),s=r(3335);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}function l(e){let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/1e3);if(a<60)return"just now";let s=Math.floor(a/60);if(s<60)return"".concat(s,"m ago");let n=Math.floor(s/60);if(n<24)return"".concat(n,"h ago");let l=Math.floor(n/24);return l<7?"".concat(l,"d ago"):new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},1770:function(e,t,r){"use strict";r.d(t,{t:function(){return o}});var a=r(9625),s=r(6885),n=r(3227);class l{async login(e){return(await n.x.post("/auth/login",e)).data}async register(e){return(await n.x.post("/auth/register",e)).data}async logout(){await n.x.post("/auth/logout")}async getCurrentUser(){return(await n.x.get("/auth/me")).data}async refreshToken(e){return(await n.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await n.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await n.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let i=new l,o=(0,a.Ue)()((0,s.tJ)((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await i.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await i.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await i.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await i.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:a}=t();a&&e({user:{...a,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))},1331:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))});t.Z=s}},function(e){e.O(0,[64,565,971,117,744],function(){return e(e.s=7204)}),_N_E=e.O()}]);