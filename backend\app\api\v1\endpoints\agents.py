"""
Agent endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_async_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.agent import Agent, AgentType, AgentStatus
from app.services.agent_service import AgentService
from app.agents.agent_factory import AgentFactory
from app.core.exceptions import NotFoundError, ValidationError

router = APIRouter()


class AgentCreate(BaseModel):
    name: str
    description: Optional[str] = None
    agent_type: AgentType
    model: str = "gpt-3.5-turbo"
    system_prompt: Optional[str] = None
    temperature: str = "0.7"
    max_tokens: int = 2048
    capabilities: Optional[List[str]] = []
    tools: Optional[List[dict]] = []
    config: Optional[dict] = {}
    environment_vars: Optional[dict] = {}
    is_public: bool = False
    max_execution_time: int = 300
    max_memory_mb: int = 512
    max_cpu_percent: int = 50


class AgentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    temperature: Optional[str] = None
    max_tokens: Optional[int] = None
    capabilities: Optional[List[str]] = None
    tools: Optional[List[dict]] = None
    config: Optional[dict] = None
    environment_vars: Optional[dict] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None
    max_execution_time: Optional[int] = None
    max_memory_mb: Optional[int] = None
    max_cpu_percent: Optional[int] = None


class AgentExecuteRequest(BaseModel):
    messages: List[dict]
    context: Optional[dict] = {}


@router.get("/", response_model=List[dict])
async def get_agents(
    skip: int = 0,
    limit: int = 100,
    agent_type: Optional[AgentType] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's agents"""
    agent_service = AgentService(db)
    agents = await agent_service.get_by_owner(
        owner_id=current_user.id,
        skip=skip,
        limit=limit,
        agent_type=agent_type,
        is_active=is_active
    )
    return [agent.to_dict() for agent in agents]


@router.get("/public", response_model=List[dict])
async def get_public_agents(
    skip: int = 0,
    limit: int = 100,
    agent_type: Optional[AgentType] = None,
    db: AsyncSession = Depends(get_async_db)
):
    """Get public agents"""
    agent_service = AgentService(db)
    agents = await agent_service.get_public_agents(
        skip=skip,
        limit=limit,
        agent_type=agent_type
    )
    return [agent.to_dict() for agent in agents]


@router.get("/types")
async def get_agent_types():
    """Get supported agent types"""
    return {
        "types": [
            {
                "value": agent_type.value,
                "name": agent_type.value.replace("_", " ").title(),
                "description": get_agent_type_description(agent_type)
            }
            for agent_type in AgentFactory.get_supported_types()
        ]
    }


@router.post("/", response_model=dict)
async def create_agent(
    agent_data: AgentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new agent"""
    agent_service = AgentService(db)
    
    # Prepare agent data
    create_data = agent_data.dict()
    create_data["owner_id"] = current_user.id
    create_data["status"] = AgentStatus.IDLE
    
    try:
        agent = await agent_service.create(create_data)
        return agent.to_dict()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create agent: {str(e)}"
        )


@router.get("/{agent_id}", response_model=dict)
async def get_agent(
    agent_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get agent by ID"""
    agent_service = AgentService(db)
    
    try:
        agent = await agent_service.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Check permissions
        if agent.owner_id != current_user.id and not agent.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return agent.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )


@router.put("/{agent_id}", response_model=dict)
async def update_agent(
    agent_id: int,
    agent_data: AgentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Update agent"""
    agent_service = AgentService(db)
    
    try:
        agent = await agent_service.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Check permissions
        if agent.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update agent
        update_data = {k: v for k, v in agent_data.dict().items() if v is not None}
        updated_agent = await agent_service.update(agent_id, update_data)
        
        return updated_agent.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )


@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete agent"""
    agent_service = AgentService(db)
    
    try:
        agent = await agent_service.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Check permissions
        if agent.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        await agent_service.delete(agent_id)
        return {"message": "Agent deleted successfully"}
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )


@router.get("/{agent_id}/stats", response_model=dict)
async def get_agent_stats(
    agent_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get agent statistics"""
    agent_service = AgentService(db)
    
    try:
        agent = await agent_service.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Check permissions
        if agent.owner_id != current_user.id and not agent.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        stats = await agent_service.get_agent_stats(agent_id)
        return stats
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )


@router.post("/{agent_id}/execute")
async def execute_agent(
    agent_id: int,
    request: AgentExecuteRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Execute agent with given messages"""
    agent_service = AgentService(db)
    
    try:
        agent = await agent_service.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Check permissions
        if agent.owner_id != current_user.id and not agent.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Check if agent is available
        if agent.status != AgentStatus.IDLE:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Agent is currently busy"
            )
        
        # Update agent status
        await agent_service.update_status(agent_id, AgentStatus.RUNNING)
        
        # Execute agent in background
        from app.tasks.agent_tasks import execute_agent_task
        
        context = request.context or {}
        context["user_id"] = current_user.id
        
        task = execute_agent_task.delay(
            agent_id=agent_id,
            messages=request.messages,
            context=context
        )
        
        return {
            "message": "Agent execution started",
            "task_id": task.id,
            "agent_id": agent_id
        }
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )


def get_agent_type_description(agent_type: AgentType) -> str:
    """Get description for agent type"""
    descriptions = {
        AgentType.GENERAL: "General-purpose AI assistant for various tasks",
        AgentType.CODE: "Specialized for code generation, debugging, and analysis",
        AgentType.RESEARCH: "Focused on research, data analysis, and information gathering",
        AgentType.CREATIVE: "Optimized for creative writing, brainstorming, and content creation",
        AgentType.ANALYSIS: "Designed for data analysis, reporting, and insights",
        AgentType.AUTOMATION: "Built for workflow automation and task orchestration",
        AgentType.CUSTOM: "Customizable agent for specific use cases"
    }
    return descriptions.get(agent_type, "Custom agent type")
