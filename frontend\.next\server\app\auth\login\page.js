(()=>{var e={};e.id=716,e.ids=[716],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9491:e=>{"use strict";e.exports=require("assert")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1017:e=>{"use strict";e.exports=require("path")},2781:e=>{"use strict";e.exports=require("stream")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},2491:()=>{},1987:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var o=r(1355),n=r(862),a=r(5745),s=r.n(a),i=r(4635),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8746)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2808)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,6608,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\auth\\login\\page.tsx"],u="/auth/login/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5747:(e,t,r)=>{Promise.resolve().then(r.bind(r,9366))},8828:(e,t,r)=>{Promise.resolve().then(r.bind(r,5503))},272:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3907,23)),Promise.resolve().then(r.t.bind(r,7648,23)),Promise.resolve().then(r.t.bind(r,1944,23)),Promise.resolve().then(r.t.bind(r,9297,23)),Promise.resolve().then(r.t.bind(r,2649,23)),Promise.resolve().then(r.t.bind(r,4423,23))},9366:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var o=r(5452),n=r(2339),a=r(692),s=r(2652);(function(){var e=Error("Cannot find module 'react-hook-form'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@hookform/resolvers/zod'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'zod'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '__barrel_optimize__?names=EyeIcon,EyeSlashIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(4959),c=r(3757);let l=Object(function(){var e=Error("Cannot find module 'zod'");throw e.code="MODULE_NOT_FOUND",e}()).object({email:Object(function(){var e=Error("Cannot find module 'zod'");throw e.code="MODULE_NOT_FOUND",e}()).string().email("Please enter a valid email address"),password:Object(function(){var e=Error("Cannot find module 'zod'");throw e.code="MODULE_NOT_FOUND",e}()).string().min(1,"Password is required")});function d(){let[e,t]=(0,n.useState)(!1),r=(0,a.useRouter)(),{login:d,user:u,isLoading:h}=(0,i.t)(),{register:m,handleSubmit:f,formState:{errors:p,isSubmitting:x}}=Object(function(){var e=Error("Cannot find module 'react-hook-form'");throw e.code="MODULE_NOT_FOUND",e}())({resolver:Object(function(){var e=Error("Cannot find module '@hookform/resolvers/zod'");throw e.code="MODULE_NOT_FOUND",e}())(l)}),g=async e=>{try{await d(e),Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).success("Welcome back!"),r.push("/dashboard")}catch(e){Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(e.response?.data?.detail||"Login failed")}};return h?o.jsx("div",{className:"min-h-screen flex items-center justify-center",children:o.jsx(c.T,{size:"lg"})}):u?null:o.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,o.jsxs)("div",{children:[o.jsx("div",{className:"flex justify-center",children:(0,o.jsxs)(s.default,{href:"/",className:"flex items-center space-x-2",children:[o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=EyeIcon,EyeSlashIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-12 w-12 text-blue-600"}),o.jsx("span",{className:"text-3xl font-bold text-gray-900",children:"Agentico"})]})}),o.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,o.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",o.jsx(s.default,{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,o.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f(g),children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[o.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),o.jsx("input",{...m("email"),type:"email",autoComplete:"email",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your email"}),p.email&&o.jsx("p",{className:"mt-1 text-sm text-red-600",children:p.email.message})]}),(0,o.jsxs)("div",{children:[o.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,o.jsxs)("div",{className:"mt-1 relative",children:[o.jsx("input",{...m("password"),type:e?"text":"password",autoComplete:"current-password",className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your password"}),o.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=EyeIcon,EyeSlashIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 text-gray-400"}):o.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=EyeIcon,EyeSlashIcon,SparklesIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 text-gray-400"})})]}),p.password&&o.jsx("p",{className:"mt-1 text-sm text-red-600",children:p.password.message})]})]}),o.jsx("div",{className:"flex items-center justify-between",children:o.jsx("div",{className:"text-sm",children:o.jsx(s.default,{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})}),o.jsx("div",{children:o.jsx("button",{type:"submit",disabled:x,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?o.jsx(c.T,{size:"sm"}):"Sign in"})}),o.jsx("div",{className:"text-center",children:(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",o.jsx(s.default,{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up here"})]})})]})]})})}},5503:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});var o=r(5452);(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(8268),a=r(2339);function s({children:e}){let[t]=(0,a.useState)(()=>Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}()),{client:t,children:[o.jsx(n.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),o.jsx(Object(function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}()),{initialIsOpen:!1})]})}},3757:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var o=r(5452),n=r(8219);let a={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function s({size:e="md",className:t}){return o.jsx("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",a[e],t)})}},200:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var o=r(5173);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class a{constructor(){this.client=o.Z.create({baseURL:`${n}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);r?.token&&(e.headers.Authorization=`Bearer ${r.token}`)}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:r}=JSON.parse(e);if(r?.refreshToken){let{access_token:e,refresh_token:o}=(await this.client.post("/auth/refresh",{refresh_token:r.refreshToken})).data,n={...r,token:e,refreshToken:o};return localStorage.setItem("auth-storage",JSON.stringify({state:n,version:0})),t.headers.Authorization=`Bearer ${e}`,this.client(t)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Bad request");break;case 401:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Authentication required");break;case 403:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Access denied");break;case 404:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Resource not found");break;case 422:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"Validation error");break;case 429:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Too many requests. Please try again later.");break;case 500:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Server error. Please try again later.");break;default:Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error(r.detail||"An error occurred")}}else e.request?Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("Network error. Please check your connection."):Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()).error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let o=new FormData;return o.append("file",t),this.client.post(e,o,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}}let s=new a},8219:(e,t,r)=>{"use strict";r.d(t,{SY:()=>s,cn:()=>a});var o=r(8229),n=r(3880);function a(...e){return(0,n.m6)((0,o.W)(e))}function s(e){let t=new Date,r=new Date(e),o=Math.floor((t.getTime()-r.getTime())/1e3);if(o<60)return"just now";let n=Math.floor(o/60);if(n<60)return`${n}m ago`;let a=Math.floor(n/60);if(a<24)return`${a}h ago`;let s=Math.floor(a/24);return s<7?`${s}d ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},4959:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var o=r(200);class n{async login(e){return(await o.x.post("/auth/login",e)).data}async register(e){return(await o.x.post("/auth/register",e)).data}async logout(){await o.x.post("/auth/logout")}async getCurrentUser(){return(await o.x.get("/auth/me")).data}async refreshToken(e){return(await o.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await o.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await o.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let a=new n;(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}();let s=Object(function(){var e=Error("Cannot find module 'zustand'");throw e.code="MODULE_NOT_FOUND",e}())()(Object(function(){var e=Error("Cannot find module 'zustand/middleware'");throw e.code="MODULE_NOT_FOUND",e}())((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await a.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await a.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await a.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await a.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:o}=t();o&&e({user:{...o,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))},8746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>a,default:()=>i});var o=r(7873);let n=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\auth\login\page.tsx`),{__esModule:a,$$typeof:s}=n;n.default;let i=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\auth\login\page.tsx#default`)},2808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var o=r(1949),n=r(8898),a=r.n(n);r(98);var s=r(7873);let i=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx`),{__esModule:c,$$typeof:l}=i;i.default;let d=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let u={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function h({children:e}){return o.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:o.jsx("body",{className:a().className,children:(0,o.jsxs)(d,{children:[e,o.jsx(Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()),{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},98:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[341,171],()=>r(1987));module.exports=o})();