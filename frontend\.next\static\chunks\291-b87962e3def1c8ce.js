"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[291],{5836:function(e,t,r){r.d(t,{c:function(){return w}});var a=r(7437),s=r(2265),n=r(7648),o=r(9376),i=r(9778),c=r(1331),l=r(3767),d=r(7626),h=r(9152),u=r(2087),g=r(7165),x=r(4355),m=r(338),f=r(1770),p=r(3448);let y=[{name:"Dashboard",href:"/dashboard",icon:i.Z},{name:"Agents",href:"/dashboard/agents",icon:c.Z},{name:"Conversations",href:"/dashboard/conversations",icon:l.Z},{name:"Files",href:"/dashboard/files",icon:d.Z},{name:"<PERSON><PERSON><PERSON>",href:"/dashboard/settings",icon:h.Z}];function w(e){let{children:t}=e,[r,i]=(0,s.useState)(!1),c=(0,o.usePathname)(),{user:l,logout:d}=(0,f.t)();return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:(0,p.cn)("fixed inset-0 z-50 lg:hidden",r?"block":"hidden"),children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,a.jsxs)(n.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[(0,a.jsx)(u.Z,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]}),(0,a.jsx)("button",{onClick:()=>i(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(g.Z,{className:"h-6 w-6"})})]}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(e=>{let t=c===e.href;return(0,a.jsxs)(n.default,{href:e.href,className:(0,p.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>i(!1),children:[(0,a.jsx)(e.icon,{className:(0,p.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})]}),(0,a.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,a.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[(0,a.jsx)("div",{className:"flex h-16 items-center px-4",children:(0,a.jsxs)(n.default,{href:"/dashboard",className:"flex items-center space-x-2",children:[(0,a.jsx)(u.Z,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Agentico"})]})}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(e=>{let t=c===e.href;return(0,a.jsxs)(n.default,{href:e.href,className:(0,p.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",t?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,a.jsx)(e.icon,{className:(0,p.cn)("mr-3 h-6 w-6 flex-shrink-0",t?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),e.name]},e.name)})})]})}),(0,a.jsxs)("div",{className:"lg:pl-64",children:[(0,a.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("button",{onClick:()=>i(!0),className:"text-gray-500 hover:text-gray-600 lg:hidden",children:(0,a.jsx)(x.Z,{className:"h-6 w-6"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"h-5 w-5 text-white"})})}),(0,a.jsxs)("div",{className:"hidden md:block",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==l?void 0:l.full_name)||(null==l?void 0:l.username)}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:null==l?void 0:l.email})]}),(0,a.jsx)("button",{onClick:d,className:"text-sm text-gray-500 hover:text-gray-700",children:"Sign out"})]})})})]})}),(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"py-6",children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:t})})})]})]})}},7692:function(e,t,r){r.d(t,{T:function(){return o}});var a=r(7437),s=r(3448);let n={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function o(e){let{size:t="md",className:r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",n[t],r)})}},3227:function(e,t,r){r.d(t,{x:function(){return i}});var a=r(3464),s=r(9064);let n=r(257).env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class o{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);(null==r?void 0:r.token)&&(e.headers.Authorization="Bearer ".concat(r.token))}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){r._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:t}=JSON.parse(e);if(null==t?void 0:t.refreshToken){let{access_token:e,refresh_token:a}=(await this.client.post("/auth/refresh",{refresh_token:t.refreshToken})).data,s={...t,token:e,refreshToken:a};return localStorage.setItem("auth-storage",JSON.stringify({state:s,version:0})),r.headers.Authorization="Bearer ".concat(e),this.client(r)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:s.Am.error(r.detail||"Bad request");break;case 401:s.Am.error("Authentication required");break;case 403:s.Am.error("Access denied");break;case 404:s.Am.error("Resource not found");break;case 422:s.Am.error(r.detail||"Validation error");break;case 429:s.Am.error("Too many requests. Please try again later.");break;case 500:s.Am.error("Server error. Please try again later.");break;default:s.Am.error(r.detail||"An error occurred")}}else e.request?s.Am.error("Network error. Please check your connection."):s.Am.error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let a=new FormData;return a.append("file",t),this.client.post(e,a,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}constructor(){this.client=a.Z.create({baseURL:"".concat(n,"/api/v1"),timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let i=new o},3448:function(e,t,r){r.d(t,{SY:function(){return o},cn:function(){return n}});var a=r(1994),s=r(3335);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}function o(e){let t=new Date,r=new Date(e),a=Math.floor((t.getTime()-r.getTime())/1e3);if(a<60)return"just now";let s=Math.floor(a/60);if(s<60)return"".concat(s,"m ago");let n=Math.floor(s/60);if(n<24)return"".concat(n,"h ago");let o=Math.floor(n/24);return o<7?"".concat(o,"d ago"):new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},1244:function(e,t,r){r.d(t,{G:function(){return n}});var a=r(3227);class s{async getAgents(e){return(await a.x.get("/agents",{params:e})).data}async getPublicAgents(e){return(await a.x.get("/agents/public",{params:e})).data}async getAgentTypes(){return(await a.x.get("/agents/types")).data}async getAgent(e){return(await a.x.get("/agents/".concat(e))).data}async createAgent(e){return(await a.x.post("/agents",e)).data}async updateAgent(e,t){return(await a.x.put("/agents/".concat(e),t)).data}async deleteAgent(e){await a.x.delete("/agents/".concat(e))}async getAgentStats(e){return(await a.x.get("/agents/".concat(e,"/stats"))).data}async executeAgent(e,t){return(await a.x.post("/agents/".concat(e,"/execute"),t)).data}async searchAgents(e,t){return(await a.x.get("/agents/search",{params:{q:e,...t}})).data}}let n=new s},1770:function(e,t,r){r.d(t,{t:function(){return c}});var a=r(9625),s=r(6885),n=r(3227);class o{async login(e){return(await n.x.post("/auth/login",e)).data}async register(e){return(await n.x.post("/auth/register",e)).data}async logout(){await n.x.post("/auth/logout")}async getCurrentUser(){return(await n.x.get("/auth/me")).data}async refreshToken(e){return(await n.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await n.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await n.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let i=new o,c=(0,a.Ue)()((0,s.tJ)((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await i.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await i.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await i.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await i.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:a}=t();a&&e({user:{...a,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))}}]);