"""
Agent factory for creating specialized agent instances
"""

from typing import Dict, Type, Optional
from app.models.agent import Agent, AgentType
from app.agents.base_agent import BaseAgent
from app.agents.llm_agent import LLMAgent, CodeAgent, ResearchAgent
from app.core.exceptions import ValidationError


class AgentFactory:
    """Factory for creating agent instances"""
    
    # Registry of agent types to classes
    _agent_registry: Dict[AgentType, Type[BaseAgent]] = {
        AgentType.GENERAL: LLMAgent,
        AgentType.CODE: CodeAgent,
        AgentType.RESEARCH: ResearchAgent,
        AgentType.CREATIVE: LLMAgent,
        AgentType.ANALYSIS: ResearchAgent,
        AgentType.AUTOMATION: LLMAgent,
        AgentType.CUSTOM: LLMAgent,
    }
    
    @classmethod
    def create_agent(cls, agent: Agent) -> BaseAgent:
        """Create agent instance based on agent type"""
        agent_type = agent.agent_type
        
        if agent_type not in cls._agent_registry:
            raise ValidationError(f"Unsupported agent type: {agent_type}")
        
        agent_class = cls._agent_registry[agent_type]
        return agent_class(agent)
    
    @classmethod
    def register_agent_type(cls, agent_type: AgentType, agent_class: Type[BaseAgent]):
        """Register a new agent type"""
        cls._agent_registry[agent_type] = agent_class
    
    @classmethod
    def get_supported_types(cls) -> list[AgentType]:
        """Get list of supported agent types"""
        return list(cls._agent_registry.keys())
    
    @classmethod
    def get_agent_class(cls, agent_type: AgentType) -> Optional[Type[BaseAgent]]:
        """Get agent class for given type"""
        return cls._agent_registry.get(agent_type)


# Agent execution engine
class AgentExecutionEngine:
    """Engine for executing agents with proper resource management"""
    
    def __init__(self):
        self.active_executions: Dict[int, BaseAgent] = {}
        self.factory = AgentFactory()
    
    async def execute_agent(
        self,
        agent: Agent,
        messages: list,
        context: dict
    ) -> BaseAgent:
        """Execute agent with given messages and context"""
        # Create agent instance
        agent_instance = self.factory.create_agent(agent)
        
        # Track active execution
        self.active_executions[agent.id] = agent_instance
        
        try:
            # Convert messages to AgentMessage objects
            from app.agents.base_agent import AgentMessage, AgentContext
            from datetime import datetime
            
            agent_messages = [
                AgentMessage(
                    role=msg.get("role", "user"),
                    content=msg.get("content", ""),
                    timestamp=datetime.fromisoformat(msg.get("timestamp", datetime.utcnow().isoformat())),
                    metadata=msg.get("metadata", {})
                )
                for msg in messages
            ]
            
            # Create execution context
            agent_context = AgentContext(
                agent_id=agent.id,
                user_id=context.get("user_id"),
                conversation_id=context.get("conversation_id"),
                task_id=context.get("task_id"),
                files=context.get("files", []),
                variables=context.get("variables", {})
            )
            
            # Execute agent
            async for response in agent_instance.execute(agent_messages, agent_context):
                yield response
                
        finally:
            # Clean up
            if agent.id in self.active_executions:
                del self.active_executions[agent.id]
    
    def is_agent_running(self, agent_id: int) -> bool:
        """Check if agent is currently running"""
        return agent_id in self.active_executions
    
    def get_active_executions(self) -> Dict[int, BaseAgent]:
        """Get all active executions"""
        return self.active_executions.copy()
    
    async def stop_agent(self, agent_id: int) -> bool:
        """Stop running agent"""
        if agent_id in self.active_executions:
            # In a real implementation, you would cancel the execution
            # For now, just remove from tracking
            del self.active_executions[agent_id]
            return True
        return False


# Global execution engine instance
execution_engine = AgentExecutionEngine()
