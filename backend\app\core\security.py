"""
Security utilities for authentication and authorization
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.core.exceptions import AuthenticationError, AuthorizationError
# from app.models.user import User
# from app.services.user_service import UserService

# Password hashing
pwd_context = CryptContext(
    schemes=settings.PWD_CONTEXT_SCHEMES,
    deprecated=settings.PWD_CONTEXT_DEPRECATED
)

# HTTP Bearer token scheme
security = HTTPBearer()


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(data: dict) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> dict:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            raise AuthenticationError("Invalid token type")
        
        # Check expiration
        exp = payload.get("exp")
        if exp is None or datetime.fromtimestamp(exp) < datetime.utcnow():
            raise AuthenticationError("Token has expired")
        
        return payload
    
    except JWTError:
        raise AuthenticationError("Invalid token")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)


# Temporarily disabled user authentication
# def get_current_user(
#     credentials: HTTPAuthorizationCredentials = Depends(security),
#     db: Session = Depends(get_db)
# ) -> User:
#     """Get current authenticated user"""
#     try:
#         # Verify token
#         payload = verify_token(credentials.credentials)
#         user_id = payload.get("sub")

#         if user_id is None:
#             raise AuthenticationError("Invalid token payload")

#         # Get user from database
#         user_service = UserService(db)
#         user = user_service.get_by_id(user_id)

#         if user is None:
#             raise AuthenticationError("User not found")

#         if not user.is_active:
#             raise AuthenticationError("User account is disabled")

#         return user

#     except Exception as e:
#         if isinstance(e, AuthenticationError):
#             raise e
#         raise AuthenticationError("Authentication failed")


# def get_current_active_user(
#     current_user: User = Depends(get_current_user)
# ) -> User:
#     """Get current active user"""
#     if not current_user.is_active:
#         raise AuthenticationError("User account is disabled")
#     return current_user


# def get_current_superuser(
#     current_user: User = Depends(get_current_user)
# ) -> User:
#     """Get current superuser"""
#     if not current_user.is_superuser:
#         raise AuthorizationError("Insufficient permissions")
#     return current_user


def require_permissions(*permissions: str):
    """Decorator to require specific permissions"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get current user from kwargs
            current_user = kwargs.get("current_user")
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            # Check permissions
            user_permissions = set(current_user.permissions or [])
            required_permissions = set(permissions)
            
            if not required_permissions.issubset(user_permissions) and not current_user.is_superuser:
                raise AuthorizationError(
                    f"Missing required permissions: {required_permissions - user_permissions}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class RoleChecker:
    """Role-based access control checker"""
    
    def __init__(self, allowed_roles: list[str]):
        self.allowed_roles = allowed_roles
    
    # def __call__(self, current_user: User = Depends(get_current_user)):
    #     if current_user.role not in self.allowed_roles and not current_user.is_superuser:
    #         raise AuthorizationError(
    #             f"Access denied. Required roles: {self.allowed_roles}"
    #         )
    #     return current_user
    pass
