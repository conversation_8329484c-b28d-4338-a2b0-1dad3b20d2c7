(()=>{var e={};e.id=702,e.ids=[702],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9491:e=>{"use strict";e.exports=require("assert")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1017:e=>{"use strict";e.exports=require("path")},2781:e=>{"use strict";e.exports=require("stream")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},126:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var n=r(1355),o=r(862),s=r(5745),a=r.n(s),i=r(4635),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5083)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2808)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,6608,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\page.tsx"],u="/dashboard/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3072:(e,t,r)=>{Promise.resolve().then(r.bind(r,6894))},6894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var n=r(5452);r(2339);var o=r(692),s=r(4959),a=r(9927),i=r(4491),c=r(200);class l{async getConversations(e){return(await c.x.get("/conversations",{params:e})).data}async getConversation(e){return(await c.x.get(`/conversations/${e}`)).data}async createConversation(e){return(await c.x.post("/conversations",e)).data}async updateConversation(e,t){return(await c.x.put(`/conversations/${e}`,t)).data}async deleteConversation(e){await c.x.delete(`/conversations/${e}`)}async getMessages(e,t){return(await c.x.get(`/conversations/${e}/messages`,{params:t})).data}async createMessage(e,t){return(await c.x.post(`/conversations/${e}/messages`,t)).data}async archiveConversation(e){await c.x.post(`/conversations/${e}/archive`)}async searchConversations(e,t){return(await c.x.get("/conversations/search",{params:{q:e,...t}})).data}}let d=new l;var u=r(3757),h=r(8219);let m={blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"},p={positive:"text-green-600",negative:"text-red-600",neutral:"text-gray-600"};function x({name:e,value:t,icon:r,color:o,change:s,changeType:a="neutral"}){return n.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:n.jsx("div",{className:"p-5",children:(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"flex-shrink-0",children:n.jsx("div",{className:(0,h.cn)("p-3 rounded-md",m[o]),children:n.jsx(r,{className:"h-6 w-6 text-white"})})}),n.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,n.jsxs)("dl",{children:[n.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e}),(0,n.jsxs)("dd",{className:"flex items-baseline",children:[n.jsx("div",{className:"text-2xl font-semibold text-gray-900",children:t}),s&&n.jsx("div",{className:(0,h.cn)("ml-2 text-sm font-medium",p[a]),children:s})]})]})})]})})})}(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,CpuChipIcon,ExclamationCircleIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();let g=[{id:1,type:"agent_created",title:"Created new Code Agent",description:"Python development assistant",timestamp:new Date(Date.now()-18e5).toISOString(),icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,CpuChipIcon,ExclamationCircleIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),iconColor:"text-blue-500",status:"success"},{id:2,type:"conversation_started",title:"Started conversation with Research Agent",description:"Market analysis discussion",timestamp:new Date(Date.now()-72e5).toISOString(),icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,CpuChipIcon,ExclamationCircleIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),iconColor:"text-green-500",status:"success"},{id:3,type:"task_completed",title:"Task completed successfully",description:"Data analysis report generated",timestamp:new Date(Date.now()-144e5).toISOString(),icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,CpuChipIcon,ExclamationCircleIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),iconColor:"text-green-500",status:"success"},{id:4,type:"task_failed",title:"Task execution failed",description:"API connection timeout",timestamp:new Date(Date.now()-216e5).toISOString(),icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,CpuChipIcon,ExclamationCircleIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),iconColor:"text-red-500",status:"error"}];function _(){let{data:e=g,isLoading:t}=Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({queryKey:["recent-activity"],queryFn:async()=>(await new Promise(e=>setTimeout(e,500)),g)});return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),t?n.jsx("div",{className:"flex items-center justify-center h-32",children:n.jsx(u.T,{})}):n.jsx("div",{className:"flow-root",children:n.jsx("ul",{className:"-mb-8",children:e.map((t,r)=>n.jsx("li",{children:(0,n.jsxs)("div",{className:"relative pb-8",children:[r!==e.length-1?n.jsx("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,n.jsxs)("div",{className:"relative flex space-x-3",children:[n.jsx("div",{children:n.jsx("span",{className:`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${"success"===t.status?"bg-green-500":"bg-red-500"}`,children:n.jsx(t.icon,{className:"h-5 w-5 text-white"})})}),(0,n.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,n.jsxs)("div",{children:[n.jsx("p",{className:"text-sm text-gray-900 font-medium",children:t.title}),n.jsx("p",{className:"text-sm text-gray-500",children:t.description})]}),n.jsx("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:(0,h.SY)(t.timestamp)})]})]})]})},t.id))})}),!t&&0===e.length&&n.jsx("div",{className:"text-center py-8",children:n.jsx("p",{className:"text-gray-500",children:"No recent activity"})})]})}var f=r(2652);!function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CpuChipIcon,DocumentArrowUpIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}();let b=[{name:"Create Agent",description:"Build a new AI agent for your tasks",href:"/dashboard/agents/new",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CpuChipIcon,DocumentArrowUpIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"bg-blue-500 hover:bg-blue-600"},{name:"Start Conversation",description:"Chat with your AI agents",href:"/dashboard/conversations/new",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CpuChipIcon,DocumentArrowUpIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"bg-green-500 hover:bg-green-600"},{name:"Upload Files",description:"Add files for agent processing",href:"/dashboard/files/upload",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CpuChipIcon,DocumentArrowUpIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"bg-purple-500 hover:bg-purple-600"}];function v(){return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[n.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Quick Actions"}),n.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,CpuChipIcon,DocumentArrowUpIcon,PlusIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-5 w-5 text-gray-400"})]}),n.jsx("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:b.map(e=>(0,n.jsxs)(f.default,{href:e.href,className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors",children:[n.jsx("div",{children:n.jsx("span",{className:`rounded-lg inline-flex p-3 text-white ${e.color} transition-colors`,children:n.jsx(e.icon,{className:"h-6 w-6"})})}),(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:[n.jsx("span",{className:"absolute inset-0","aria-hidden":"true"}),e.name]}),n.jsx("p",{className:"mt-2 text-sm text-gray-500",children:e.description})]})]},e.name))})]})}function C(){let{data:e,isLoading:t}=Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({queryKey:["agents"],queryFn:()=>i.G.getAgents()}),{data:r,isLoading:o}=Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({queryKey:["conversations"],queryFn:()=>d.getConversations()});if(t||o)return n.jsx("div",{className:"flex items-center justify-center h-64",children:n.jsx(u.T,{size:"lg"})});let s=[{name:"Active Agents",value:e?.filter(e=>e.is_active).length||0,icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"blue",change:"+12%",changeType:"positive"},{name:"Conversations",value:r?.length||0,icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"green",change:"+5%",changeType:"positive"},{name:"Files Processed",value:24,icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"purple",change:"+18%",changeType:"positive"},{name:"Avg Response Time",value:"2.3s",icon:Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),color:"orange",change:"-8%",changeType:"positive"}];return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),n.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Welcome back! Here's what's happening with your AI agents."})]}),n.jsx("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:s.map(e=>n.jsx(x,{...e},e.name))}),n.jsx(v,{}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[n.jsx(_,{}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Agent Performance"}),n.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx(Object(function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-12 w-12 mx-auto mb-2 text-gray-400"}),n.jsx("p",{children:"Performance charts coming soon"})]})})]})]})]})}function j(){(0,o.useRouter)();let{user:e,isLoading:t,checkAuth:r}=(0,s.t)();return t?n.jsx("div",{className:"min-h-screen flex items-center justify-center",children:n.jsx(u.T,{size:"lg"})}):e?n.jsx(a.c,{children:n.jsx(C,{})}):null}(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '__barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,CpuChipIcon,DocumentIcon!=!@heroicons/react/24/outline'");throw e.code="MODULE_NOT_FOUND",e}()},5083:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>s,default:()=>i});var n=r(7873);let o=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\page.tsx`),{__esModule:s,$$typeof:a}=o;o.default;let i=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[341,171,517],()=>r(126));module.exports=n})();