(()=>{var e={};e.id=702,e.ids=[702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},9801:e=>{"use strict";e.exports=require("os")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},4175:e=>{"use strict";e.exports=require("tty")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},7784:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(3191),a=s(8716),n=s(7922),i=s.n(n),o=s(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8256)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4968)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\dashboard\\page.tsx"],u="/dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7786:(e,t,s)=>{Promise.resolve().then(s.bind(s,4187))},4187:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Z});var r=s(326),a=s(7577),n=s(5047),i=s(4911),o=s(7368),l=s(108),c=s(8868),d=s(7710),u=s(5966);let x=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var m=s(6024),g=s(3002);class h{async getConversations(e){return(await g.x.get("/conversations",{params:e})).data}async getConversation(e){return(await g.x.get(`/conversations/${e}`)).data}async createConversation(e){return(await g.x.post("/conversations",e)).data}async updateConversation(e,t){return(await g.x.put(`/conversations/${e}`,t)).data}async deleteConversation(e){await g.x.delete(`/conversations/${e}`)}async getMessages(e,t){return(await g.x.get(`/conversations/${e}/messages`,{params:t})).data}async createMessage(e,t){return(await g.x.post(`/conversations/${e}/messages`,t)).data}async archiveConversation(e){await g.x.post(`/conversations/${e}/archive`)}async searchConversations(e,t){return(await g.x.get("/conversations/search",{params:{q:e,...t}})).data}}let p=new h;var v=s(3955),f=s(1223);let j={blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"},w={positive:"text-green-600",negative:"text-red-600",neutral:"text-gray-600"};function y({name:e,value:t,icon:s,color:a,change:n,changeType:i="neutral"}){return r.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:r.jsx("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:(0,f.cn)("p-3 rounded-md",j[a]),children:r.jsx(s,{className:"h-6 w-6 text-white"})})}),r.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[r.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e}),(0,r.jsxs)("dd",{className:"flex items-baseline",children:[r.jsx("div",{className:"text-2xl font-semibold text-gray-900",children:t}),n&&r.jsx("div",{className:(0,f.cn)("ml-2 text-sm font-medium",w[i]),children:n})]})]})})]})})})}let b=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),N=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),k=[{id:1,type:"agent_created",title:"Created new Code Agent",description:"Python development assistant",timestamp:new Date(Date.now()-18e5).toISOString(),icon:c.Z,iconColor:"text-blue-500",status:"success"},{id:2,type:"conversation_started",title:"Started conversation with Research Agent",description:"Market analysis discussion",timestamp:new Date(Date.now()-72e5).toISOString(),icon:d.Z,iconColor:"text-green-500",status:"success"},{id:3,type:"task_completed",title:"Task completed successfully",description:"Data analysis report generated",timestamp:new Date(Date.now()-144e5).toISOString(),icon:b,iconColor:"text-green-500",status:"success"},{id:4,type:"task_failed",title:"Task execution failed",description:"API connection timeout",timestamp:new Date(Date.now()-216e5).toISOString(),icon:N,iconColor:"text-red-500",status:"error"}];function C(){let{data:e=k,isLoading:t}=(0,l.a)({queryKey:["recent-activity"],queryFn:async()=>(await new Promise(e=>setTimeout(e,500)),k)});return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),t?r.jsx("div",{className:"flex items-center justify-center h-32",children:r.jsx(v.T,{})}):r.jsx("div",{className:"flow-root",children:r.jsx("ul",{className:"-mb-8",children:e.map((t,s)=>r.jsx("li",{children:(0,r.jsxs)("div",{className:"relative pb-8",children:[s!==e.length-1?r.jsx("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,r.jsxs)("div",{className:"relative flex space-x-3",children:[r.jsx("div",{children:r.jsx("span",{className:`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${"success"===t.status?"bg-green-500":"bg-red-500"}`,children:r.jsx(t.icon,{className:"h-5 w-5 text-white"})})}),(0,r.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-900 font-medium",children:t.title}),r.jsx("p",{className:"text-sm text-gray-500",children:t.description})]}),r.jsx("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:(0,f.SY)(t.timestamp)})]})]})]})},t.id))})}),!t&&0===e.length&&r.jsx("div",{className:"text-center py-8",children:r.jsx("p",{className:"text-gray-500",children:"No recent activity"})})]})}var q=s(434);let P=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12-3-3m0 0-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});var A=s(4625);let _=[{name:"Create Agent",description:"Build a new AI agent for your tasks",href:"/dashboard/agents/new",icon:c.Z,color:"bg-blue-500 hover:bg-blue-600"},{name:"Start Conversation",description:"Chat with your AI agents",href:"/dashboard/conversations/new",icon:d.Z,color:"bg-green-500 hover:bg-green-600"},{name:"Upload Files",description:"Add files for agent processing",href:"/dashboard/files/upload",icon:P,color:"bg-purple-500 hover:bg-purple-600"}];function E(){return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Quick Actions"}),r.jsx(A.Z,{className:"h-5 w-5 text-gray-400"})]}),r.jsx("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:_.map(e=>(0,r.jsxs)(q.default,{href:e.href,className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors",children:[r.jsx("div",{children:r.jsx("span",{className:`rounded-lg inline-flex p-3 text-white ${e.color} transition-colors`,children:r.jsx(e.icon,{className:"h-6 w-6"})})}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:[r.jsx("span",{className:"absolute inset-0","aria-hidden":"true"}),e.name]}),r.jsx("p",{className:"mt-2 text-sm text-gray-500",children:e.description})]})]},e.name))})]})}function D(){let{data:e,isLoading:t}=(0,l.a)({queryKey:["agents"],queryFn:()=>m.G.getAgents()}),{data:s,isLoading:a}=(0,l.a)({queryKey:["conversations"],queryFn:()=>p.getConversations()});if(t||a)return r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx(v.T,{size:"lg"})});let n=[{name:"Active Agents",value:e?.filter(e=>e.is_active).length||0,icon:c.Z,color:"blue",change:"+12%",changeType:"positive"},{name:"Conversations",value:s?.length||0,icon:d.Z,color:"green",change:"+5%",changeType:"positive"},{name:"Files Processed",value:24,icon:u.Z,color:"purple",change:"+18%",changeType:"positive"},{name:"Avg Response Time",value:"2.3s",icon:x,color:"orange",change:"-8%",changeType:"positive"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Welcome back! Here's what's happening with your AI agents."})]}),r.jsx("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:n.map(e=>r.jsx(y,{...e},e.name))}),r.jsx(E,{}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[r.jsx(C,{}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Agent Performance"}),r.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(c.Z,{className:"h-12 w-12 mx-auto mb-2 text-gray-400"}),r.jsx("p",{children:"Performance charts coming soon"})]})})]})]})]})}function Z(){(0,n.useRouter)();let{user:e,isLoading:t,checkAuth:s}=(0,i.t)();return t?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(v.T,{size:"lg"})}):e?r.jsx(o.c,{children:r.jsx(D,{})}):null}},8256:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\app\dashboard\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[497,40,882,73],()=>s(7784));module.exports=r})();