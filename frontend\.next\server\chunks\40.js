exports.id=40,exports.ids=[40],exports.modules={3930:(e,a,n)=>{e.exports={parallel:n(7638),serial:n(545),serialOrdered:n(7779)}},9774:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},1281:(e,a,n)=>{var t=n(9336);e.exports=function(e){var a=!1;return t(function(){a=!0}),function(n,i){a?e(n,i):t(function(){e(n,i)})}}},9336:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},2534:(e,a,n)=>{var t=n(1281),i=n(9774);e.exports=function(e,a,n,o){var s,r,c=n.keyedList?n.keyedList[n.index]:n.index;n.jobs[c]=(s=e[c],r=function(e,a){c in n.jobs&&(delete n.jobs[c],e?i(n):n.results[c]=a,o(e,n.results))},2==a.length?a(s,t(r)):a(s,c,t(r)))}},6807:e=>{e.exports=function(e,a){var n=!Array.isArray(e),t={index:0,keyedList:n||a?Object.keys(e):null,jobs:{},results:n?{}:[],size:n?Object.keys(e).length:e.length};return a&&t.keyedList.sort(n?a:function(n,t){return a(e[n],e[t])}),t}},9867:(e,a,n)=>{var t=n(9774),i=n(1281);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,t(this),i(e)(null,this.results))}},7638:(e,a,n)=>{var t=n(2534),i=n(6807),o=n(9867);e.exports=function(e,a,n){for(var s=i(e);s.index<(s.keyedList||e).length;)t(e,a,s,function(e,a){if(e){n(e,a);return}if(0===Object.keys(s.jobs).length){n(null,s.results);return}}),s.index++;return o.bind(s,n)}},545:(e,a,n)=>{var t=n(7779);e.exports=function(e,a,n){return t(e,a,null,n)}},7779:(e,a,n)=>{var t=n(2534),i=n(6807),o=n(9867);function s(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,n,s){var r=i(e,n);return t(e,a,r,function n(i,o){if(i){s(i,o);return}if(r.index++,r.index<(r.keyedList||e).length){t(e,a,r,n);return}s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,a){return -1*s(e,a)}},4432:(e,a,n)=>{"use strict";var t=n(9759),i=n(6802),o=n(7336),s=n(1596);e.exports=s||t.call(o,i)},6802:e=>{"use strict";e.exports=Function.prototype.apply},7336:e=>{"use strict";e.exports=Function.prototype.call},6892:(e,a,n)=>{"use strict";var t=n(9759),i=n(2823),o=n(7336),s=n(4432);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new i("a function is required");return s(t,o,e)}},1596:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},8726:(e,a,n)=>{var t=n(1764),i=n(6162).Stream,o=n(4130);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,t.inherits(s,i),s.create=function(e){var a=new this;for(var n in e=e||{})a[n]=e[n];return a},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var a=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,a){return i.prototype.pipe.call(this,e,a),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},9662:(e,a,n)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let n="color: "+this.color;a.splice(1,0,n,"color: inherit");let t=0,i=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(t++,"%c"===e&&(i=t))}),a.splice(i,0,n)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")||a.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=n(7507)(a);let{formatters:t}=e.exports;t.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},7507:(e,a,n)=>{e.exports=function(e){function a(e){let n,i,o;let s=null;function r(...e){if(!r.enabled)return;let t=Number(new Date),i=t-(n||t);r.diff=i,r.prev=n,r.curr=t,n=t,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(n,t)=>{if("%%"===n)return"%";o++;let i=a.formatters[t];if("function"==typeof i){let a=e[o];n=i.call(r,a),e.splice(o,1),o--}return n}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=t,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==a.namespaces&&(i=a.namespaces,o=a.enabled(e)),o),set:e=>{s=e}}),"function"==typeof a.init&&a.init(r),r}function t(e,n){let t=a(this.namespace+(void 0===n?":":n)+e);return t.log=this.log,t}function i(e,a){let n=0,t=0,i=-1,o=0;for(;n<e.length;)if(t<a.length&&(a[t]===e[n]||"*"===a[t]))"*"===a[t]?(i=t,o=n):n++,t++;else{if(-1===i)return!1;t=i+1,n=++o}for(;t<a.length&&"*"===a[t];)t++;return t===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let n of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===n[0]?a.skips.push(n.slice(1)):a.names.push(n)},a.enabled=function(e){for(let n of a.skips)if(i(e,n))return!1;for(let n of a.names)if(i(e,n))return!0;return!1},a.humanize=n(7914),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(n=>{a[n]=e[n]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let n=0;for(let a=0;a<e.length;a++)n=(n<<5)-n+e.charCodeAt(a)|0;return a.colors[Math.abs(n)%a.colors.length]},a.enable(a.load()),a}},8589:(e,a,n)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=n(9662):e.exports=n(6027)},6027:(e,a,n)=>{let t=n(4175),i=n(1764);a.init=function(e){e.inspectOpts={};let n=Object.keys(a.inspectOpts);for(let t=0;t<n.length;t++)e.inspectOpts[n[t]]=a.inspectOpts[n[t]]},a.log=function(...e){return process.stderr.write(i.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(n){let{namespace:t,useColors:i}=this;if(i){let a=this.color,i="\x1b[3"+(a<8?a:"8;5;"+a),o=`  ${i};1m${t} \u001B[0m`;n[0]=o+n[0].split("\n").join("\n"+o),n.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else n[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+t+" "+n[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:t.isatty(process.stderr.fd)},a.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=n(7495);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let n=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),t=process.env[a];return t=!!/^(yes|on|true|enabled)$/i.test(t)||!/^(no|off|false|disabled)$/i.test(t)&&("null"===t?null:Number(t)),e[n]=t,e},{}),e.exports=n(7507)(a);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},4130:(e,a,n)=>{var t=n(6162).Stream,i=n(1764);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,i.inherits(o,t),o.create=function(e,a){var n=new this;for(var t in a=a||{})n[t]=a[t];n.source=e;var i=e.emit;return e.emit=function(){return n._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),n.pauseStream&&e.pause(),n},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=t.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},7186:(e,a,n)=>{"use strict";var t,i=n(6892),o=n(2924);try{t=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var s=!!t&&o&&o(Object.prototype,"__proto__"),r=Object,c=r.getPrototypeOf;e.exports=s&&"function"==typeof s.get?i([s.get]):"function"==typeof c&&function(e){return c(null==e?e:r(e))}},5702:e=>{"use strict";var a=Object.defineProperty||!1;if(a)try{a({},"a",{value:1})}catch(e){a=!1}e.exports=a},1073:e=>{"use strict";e.exports=EvalError},8170:e=>{"use strict";e.exports=Error},5553:e=>{"use strict";e.exports=RangeError},6861:e=>{"use strict";e.exports=ReferenceError},7450:e=>{"use strict";e.exports=SyntaxError},2823:e=>{"use strict";e.exports=TypeError},4606:e=>{"use strict";e.exports=URIError},7713:e=>{"use strict";e.exports=Object},406:(e,a,n)=>{"use strict";var t=n(8197)("%Object.defineProperty%",!0),i=n(5247)(),o=n(7238),s=n(2823),r=i?Symbol.toStringTag:null;e.exports=function(e,a){var n=arguments.length>2&&!!arguments[2]&&arguments[2].force,i=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==n&&"boolean"!=typeof n||void 0!==i&&"boolean"!=typeof i)throw new s("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");r&&(n||!o(e,r))&&(t?t(e,r,{configurable:!i,enumerable:!1,value:a,writable:!1}):e[r]=a)}},9057:(e,a,n)=>{var t;e.exports=function(){if(!t){try{t=n(8589)("follow-redirects")}catch(e){}"function"!=typeof t&&(t=function(){})}t.apply(null,arguments)}},9831:(e,a,n)=>{var t=n(7360),i=t.URL,o=n(2615),s=n(8791),r=n(6162).Writable,c=n(7790),p=n(9057);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,n=C(Error.captureStackTrace);e||!a&&n||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new i(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,n,t){this._redirectable.emit(e,a,n,t)}});var f=S("ERR_INVALID_URL","Invalid URL",TypeError),x=S("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),h=S("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",x),v=S("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=S("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||_;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var n=this;this._onNativeResponse=function(e){try{n._processResponse(e)}catch(e){n.emit("error",e instanceof x?e:new x({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach(function(t){var o=t+":",s=n[o]=e[t],r=a[t]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,t,s){var r;return(r=e,i&&r instanceof i)?e=j(e):A(e)?e=j(E(e)):(s=t,t=k(e),e={protocol:o}),C(t)&&(s=t,t=null),(t=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,t)).nativeProtocols=n,A(t.host)||A(t.hostname)||(t.hostname="::1"),c.equal(t.protocol,o,"protocol mismatch"),p("options",t),new y(t,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,n){var t=r.request(e,a,n);return t.end(),t},configurable:!0,enumerable:!0,writable:!0}})}),a}function _(){}function E(e){var a;if(l)a=new i(e);else if(!A((a=k(t.parse(e))).protocol))throw new f({input:e});return a}function k(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function j(e,a){var n=a||{};for(var t of u)n[t]=e[t];return n.hostname.startsWith("[")&&(n.hostname=n.hostname.slice(1,-1)),""!==n.port&&(n.port=Number(n.port)),n.path=n.search?n.pathname+n.search:n.pathname,n}function R(e,a){var n;for(var t in a)e.test(t)&&(n=a[t],delete a[t]);return null==n?void 0:String(n).trim()}function S(e,a,n){function t(n){C(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return t.prototype=new(n||Error),Object.defineProperties(t.prototype,{constructor:{value:t,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),t}function O(e,a){for(var n of d)e.removeListener(n,m[n]);e.on("error",_),e.destroy(a)}function A(e){return"string"==typeof e||e instanceof String}function C(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){O(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return O(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,n){if(this._ending)throw new b;if(!A(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(C(a)&&(n=a,a=null),0===e.length){n&&n();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,n)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,n){if(C(e)?(n=e,e=a=null):C(a)&&(n=a,a=null),e){var t=this,i=this._currentRequest;this.write(e,a,function(){t._ended=!0,i.end(null,null,n)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var n=this;function t(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function i(a){n._timeout&&clearTimeout(n._timeout),n._timeout=setTimeout(function(){n.emit("timeout"),o()},e),t(a)}function o(){n._timeout&&(clearTimeout(n._timeout),n._timeout=null),n.removeListener("abort",o),n.removeListener("error",o),n.removeListener("response",o),n.removeListener("close",o),a&&n.removeListener("timeout",a),n.socket||n._currentRequest.removeListener("socket",i)}return a&&this.on("timeout",a),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",t),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,n){return this._currentRequest[e](a,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var n=e.slice(0,-1);this._options.agent=this._options.agents[n]}var i=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var o of(i._redirectable=this,d))i.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?t.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,c=this._requestBodyBuffers;!function e(a){if(i===r._currentRequest){if(a)r.emit("error",a);else if(s<c.length){var n=c[s++];i.finished||i.write(n.data,n.encoding,e)}else r._ended&&i.end()}}()}},y.prototype._processResponse=function(e){var a,n,o,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var r=e.headers.location;if(!r||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(O(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new h;var u=this._options.beforeRedirect;u&&(o=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],R(/^content-/i,this._options.headers));var m=R(/^host$/i,this._options.headers),f=E(this._currentUrl),x=m||f.host,v=/^\w+:/.test(r)?this._currentUrl:t.format(Object.assign(f,{host:x})),b=l?new i(r,v):E(t.resolve(v,r));if(p("redirecting to",b.href),this._isRedirect=!0,j(b,this._options),(b.protocol===f.protocol||"https:"===b.protocol)&&(b.host===x||(c(A(a=b.host)&&A(x)),(n=a.length-x.length-1)>0&&"."===a[n]&&a.endsWith(x)))||R(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),C(u)){var g={headers:e.headers,statusCode:s},y={url:v,method:d,headers:o};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},4127:(e,a,n)=>{var t=n(8726),i=n(1764),o=n(5315),s=n(2615),r=n(8791),c=n(7360).parse,p=n(2048),l=n(6162).Stream,u=n(45),d=n(3930),m=n(406),f=n(6578);function x(e){if(!(this instanceof x))return new x(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],t.call(this),e=e||{})this[a]=e[a]}e.exports=x,i.inherits(x,t),x.LINE_BREAK="\r\n",x.DEFAULT_CONTENT_TYPE="application/octet-stream",x.prototype.append=function(e,a,n){"string"==typeof(n=n||{})&&(n={filename:n});var i=t.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,n),s=this._multiPartFooter();i(o),i(a),i(s),this._trackLength(o,a,n)},x.prototype._trackLength=function(e,a,n){var t=0;null!=n.knownLength?t+=+n.knownLength:Buffer.isBuffer(a)?t=a.length:"string"==typeof a&&(t=Buffer.byteLength(a)),this._valueLength+=t,this._overheadLength+=Buffer.byteLength(e)+x.LINE_BREAK.length,a&&(a.path||a.readable&&Object.prototype.hasOwnProperty.call(a,"httpVersion")||a instanceof l)&&(n.knownLength||this._valuesToMeasure.push(a))},x.prototype._lengthRetriever=function(e,a){Object.prototype.hasOwnProperty.call(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(n,t){if(n){a(n);return}a(null,t.size-(e.start?e.start:0))}):Object.prototype.hasOwnProperty.call(e,"httpVersion")?a(null,+e.headers["content-length"]):Object.prototype.hasOwnProperty.call(e,"httpModule")?(e.on("response",function(n){e.pause(),a(null,+n.headers["content-length"])}),e.resume()):a("Unknown stream")},x.prototype._multiPartHeader=function(e,a,n){if("string"==typeof n.header)return n.header;var t,i=this._getContentDisposition(a,n),o=this._getContentType(a,n),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof n.header&&f(r,n.header),r)if(Object.prototype.hasOwnProperty.call(r,c)){if(null==(t=r[c]))continue;Array.isArray(t)||(t=[t]),t.length&&(s+=c+": "+t.join("; ")+x.LINE_BREAK)}return"--"+this.getBoundary()+x.LINE_BREAK+s+x.LINE_BREAK},x.prototype._getContentDisposition=function(e,a){var n,t;return"string"==typeof a.filepath?n=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?n=o.basename(a.filename||e.name||e.path):e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=o.basename(e.client._httpMessage.path||"")),n&&(t='filename="'+n+'"'),t},x.prototype._getContentType=function(e,a){var n=a.contentType;return!n&&e.name&&(n=u.lookup(e.name)),!n&&e.path&&(n=u.lookup(e.path)),!n&&e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=e.headers["content-type"]),!n&&(a.filepath||a.filename)&&(n=u.lookup(a.filepath||a.filename)),n||"object"!=typeof e||(n=x.DEFAULT_CONTENT_TYPE),n},x.prototype._multiPartFooter=function(){return(function(e){var a=x.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},x.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+x.LINE_BREAK},x.prototype.getHeaders=function(e){var a,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)Object.prototype.hasOwnProperty.call(e,a)&&(n[a.toLowerCase()]=e[a]);return n},x.prototype.setBoundary=function(e){this._boundary=e},x.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},x.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),n=0,t=this._streams.length;n<t;n++)"function"!=typeof this._streams[n]&&(Buffer.isBuffer(this._streams[n])?e=Buffer.concat([e,this._streams[n]]):e=Buffer.concat([e,Buffer.from(this._streams[n])]),("string"!=typeof this._streams[n]||this._streams[n].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(x.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},x.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},x.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},x.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},x.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(n,t){if(n){e(n);return}t.forEach(function(e){a+=e}),e(null,a)})},x.prototype.submit=function(e,a){var n,t,i={method:"post"};return"string"==typeof e?t=f({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(t=f(e,i)).port||(t.port="https:"==t.protocol?443:80),t.headers=this.getHeaders(e.headers),n="https:"==t.protocol?r.request(t):s.request(t),this.getLength((function(e,t){if(e&&"Unknown stream"!==e){this._error(e);return}if(t&&n.setHeader("Content-Length",t),this.pipe(n),a){var i,o=function(e,t){return n.removeListener("error",o),n.removeListener("response",i),a.call(this,e,t)};i=o.bind(this,null),n.on("error",o),n.on("response",i)}}).bind(this)),n},x.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},x.prototype.toString=function(){return"[object FormData]"},m(x,"FormData")},6578:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(n){e[n]=e[n]||a[n]}),e}},4930:e=>{"use strict";var a=Object.prototype.toString,n=Math.max,t=function(e,a){for(var n=[],t=0;t<e.length;t+=1)n[t]=e[t];for(var i=0;i<a.length;i+=1)n[i+e.length]=a[i];return n},i=function(e,a){for(var n=[],t=a||0,i=0;t<e.length;t+=1,i+=1)n[i]=e[t];return n},o=function(e,a){for(var n="",t=0;t<e.length;t+=1)n+=e[t],t+1<e.length&&(n+=a);return n};e.exports=function(e){var s,r=this;if("function"!=typeof r||"[object Function]"!==a.apply(r))throw TypeError("Function.prototype.bind called on incompatible "+r);for(var c=i(arguments,1),p=n(0,r.length-c.length),l=[],u=0;u<p;u++)l[u]="$"+u;if(s=Function("binder","return function ("+o(l,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof s){var a=r.apply(this,t(c,arguments));return Object(a)===a?a:this}return r.apply(e,t(c,arguments))}),r.prototype){var d=function(){};d.prototype=r.prototype,s.prototype=new d,d.prototype=null}return s}},9759:(e,a,n)=>{"use strict";var t=n(4930);e.exports=Function.prototype.bind||t},8197:(e,a,n)=>{"use strict";var t,i=n(7713),o=n(8170),s=n(1073),r=n(5553),c=n(6861),p=n(7450),l=n(2823),u=n(4606),d=n(5135),m=n(3084),f=n(3626),x=n(1794),h=n(1887),v=n(9953),b=n(9944),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=n(2924),_=n(5702),E=function(){throw new l},k=w?function(){try{return arguments.callee,E}catch(e){try{return w(arguments,"callee").get}catch(e){return E}}}():E,j=n(6514)(),R=n(8644),S=n(2230),O=n(1475),A=n(6802),C=n(7336),T={},P="undefined"!=typeof Uint8Array&&R?R(Uint8Array):t,z={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?t:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?t:ArrayBuffer,"%ArrayIteratorPrototype%":j&&R?R([][Symbol.iterator]()):t,"%AsyncFromSyncIteratorPrototype%":t,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?t:Atomics,"%BigInt%":"undefined"==typeof BigInt?t:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?t:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?t:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?t:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":s,"%Float16Array%":"undefined"==typeof Float16Array?t:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?t:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?t:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?t:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?t:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?t:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?t:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":j&&R?R(R([][Symbol.iterator]())):t,"%JSON%":"object"==typeof JSON?JSON:t,"%Map%":"undefined"==typeof Map?t:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&j&&R?R(new Map()[Symbol.iterator]()):t,"%Math%":Math,"%Number%":Number,"%Object%":i,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?t:Promise,"%Proxy%":"undefined"==typeof Proxy?t:Proxy,"%RangeError%":r,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?t:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?t:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&j&&R?R(new Set()[Symbol.iterator]()):t,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?t:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":j&&R?R(""[Symbol.iterator]()):t,"%Symbol%":j?Symbol:t,"%SyntaxError%":p,"%ThrowTypeError%":k,"%TypedArray%":P,"%TypeError%":l,"%Uint8Array%":"undefined"==typeof Uint8Array?t:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?t:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?t:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?t:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?t:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?t:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?t:WeakSet,"%Function.prototype.call%":C,"%Function.prototype.apply%":A,"%Object.defineProperty%":_,"%Object.getPrototypeOf%":S,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":f,"%Math.min%":x,"%Math.pow%":h,"%Math.round%":v,"%Math.sign%":b,"%Reflect.getPrototypeOf%":O};if(R)try{null.error}catch(e){var N=R(R(e));z["%Error.prototype%"]=N}var F=function e(a){var n;if("%AsyncFunction%"===a)n=y("async function () {}");else if("%GeneratorFunction%"===a)n=y("function* () {}");else if("%AsyncGeneratorFunction%"===a)n=y("async function* () {}");else if("%AsyncGenerator%"===a){var t=e("%AsyncGeneratorFunction%");t&&(n=t.prototype)}else if("%AsyncIteratorPrototype%"===a){var i=e("%AsyncGenerator%");i&&R&&(n=R(i.prototype))}return z[a]=n,n},L={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},I=n(9759),U=n(7238),B=I.call(C,Array.prototype.concat),D=I.call(A,Array.prototype.splice),M=I.call(C,String.prototype.replace),q=I.call(C,String.prototype.slice),W=I.call(C,RegExp.prototype.exec),H=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,G=/\\(\\)?/g,$=function(e){var a=q(e,0,1),n=q(e,-1);if("%"===a&&"%"!==n)throw new p("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==a)throw new p("invalid intrinsic syntax, expected opening `%`");var t=[];return M(e,H,function(e,a,n,i){t[t.length]=n?M(i,G,"$1"):a||e}),t},V=function(e,a){var n,t=e;if(U(L,t)&&(t="%"+(n=L[t])[0]+"%"),U(z,t)){var i=z[t];if(i===T&&(i=F(t)),void 0===i&&!a)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:t,value:i}}throw new p("intrinsic "+e+" does not exist!")};e.exports=function(e,a){if("string"!=typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof a)throw new l('"allowMissing" argument must be a boolean');if(null===W(/^%?[^%]*%?$/,e))throw new p("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=$(e),t=n.length>0?n[0]:"",i=V("%"+t+"%",a),o=i.name,s=i.value,r=!1,c=i.alias;c&&(t=c[0],D(n,B([0,1],c)));for(var u=1,d=!0;u<n.length;u+=1){var m=n[u],f=q(m,0,1),x=q(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===x||"'"===x||"`"===x)&&f!==x)throw new p("property names with quotes must have matching quotes");if("constructor"!==m&&d||(r=!0),t+="."+m,U(z,o="%"+t+"%"))s=z[o];else if(null!=s){if(!(m in s)){if(!a)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=n.length){var h=w(s,m);s=(d=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:s[m]}else d=U(s,m),s=s[m];d&&!r&&(z[o]=s)}}return s}},2230:(e,a,n)=>{"use strict";var t=n(7713);e.exports=t.getPrototypeOf||null},1475:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},8644:(e,a,n)=>{"use strict";var t=n(1475),i=n(2230),o=n(7186);e.exports=t?function(e){return t(e)}:i?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return i(e)}:o?function(e){return o(e)}:null},3534:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},2924:(e,a,n)=>{"use strict";var t=n(3534);if(t)try{t([],"length")}catch(e){t=null}e.exports=t},3285:e=>{"use strict";e.exports=(e,a=process.argv)=>{let n=e.startsWith("-")?"":1===e.length?"-":"--",t=a.indexOf(n+e),i=a.indexOf("--");return -1!==t&&(-1===i||t<i)}},6514:(e,a,n)=>{"use strict";var t="undefined"!=typeof Symbol&&Symbol,i=n(6799);e.exports=function(){return"function"==typeof t&&"function"==typeof Symbol&&"symbol"==typeof t("foo")&&"symbol"==typeof Symbol("bar")&&i()}},6799:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},a=Symbol("test"),n=Object(a);if("string"==typeof a||"[object Symbol]"!==Object.prototype.toString.call(a)||"[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var t in e[a]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var i=Object.getOwnPropertySymbols(e);if(1!==i.length||i[0]!==a||!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,a);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},5247:(e,a,n)=>{"use strict";var t=n(6799);e.exports=function(){return t()&&!!Symbol.toStringTag}},7238:(e,a,n)=>{"use strict";var t=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=n(9759);e.exports=o.call(t,i)},5135:e=>{"use strict";e.exports=Math.abs},3084:e=>{"use strict";e.exports=Math.floor},1049:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},3626:e=>{"use strict";e.exports=Math.max},1794:e=>{"use strict";e.exports=Math.min},1887:e=>{"use strict";e.exports=Math.pow},9953:e=>{"use strict";e.exports=Math.round},9944:(e,a,n)=>{"use strict";var t=n(1049);e.exports=function(e){return t(e)||0===e?e:e<0?-1:1}},6866:(e,a,n)=>{e.exports=n(572)},45:(e,a,n)=>{"use strict";var t=n(6866),i=n(5315).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),n=a&&t[a[1].toLowerCase()];return n&&n.charset?n.charset:!!(a&&s.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var n=-1===e.indexOf("/")?a.lookup(e):e;if(!n)return!1;if(-1===n.indexOf("charset")){var t=a.charset(n);t&&(n+="; charset="+t.toLowerCase())}return n},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var n=o.exec(e),t=n&&a.extensions[n[1].toLowerCase()];return!!t&&!!t.length&&t[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var n=i("x."+e).toLowerCase().substr(1);return!!n&&(a.types[n]||!1)},a.types=Object.create(null),function(e,a){var n=["nginx","apache",void 0,"iana"];Object.keys(t).forEach(function(i){var o=t[i],s=o.extensions;if(s&&s.length){e[i]=s;for(var r=0;r<s.length;r++){var c=s[r];if(a[c]){var p=n.indexOf(t[a[c]].source),l=n.indexOf(o.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=i}}})}(a.extensions,a.types)},7914:e=>{function a(e,a,n,t){return Math.round(e/n)+" "+t+(a>=1.5*n?"s":"")}e.exports=function(e,n){n=n||{};var t,i,o=typeof e;if("string"===o&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var n=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"weeks":case"week":case"w":return 6048e5*n;case"days":case"day":case"d":return 864e5*n;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*n;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}(e);if("number"===o&&isFinite(e))return n.long?(t=Math.abs(e))>=864e5?a(e,t,864e5,"day"):t>=36e5?a(e,t,36e5,"hour"):t>=6e4?a(e,t,6e4,"minute"):t>=1e3?a(e,t,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},434:(e,a,n)=>{"use strict";n.d(a,{default:()=>i.a});var t=n(9404),i=n.n(t)},5047:(e,a,n)=>{"use strict";var t=n(7389);n.o(t,"usePathname")&&n.d(a,{usePathname:function(){return t.usePathname}}),n.o(t,"useRouter")&&n.d(a,{useRouter:function(){return t.useRouter}})},3416:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"addLocale",{enumerable:!0,get:function(){return t}}),n(3658);let t=function(e){for(var a=arguments.length,n=Array(a>1?a-1:0),t=1;t<a;t++)n[t-1]=arguments[t];return e};("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},9683:(e,a,n)=>{"use strict";function t(e,a,n,t){return!1}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"getDomainLocale",{enumerable:!0,get:function(){return t}}),n(3658),("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},9404:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"default",{enumerable:!0,get:function(){return b}});let t=n(1174),i=n(326),o=t._(n(7577)),s=n(5619),r=n(944),c=n(3071),p=n(1348),l=n(3416),u=n(131),d=n(2413),m=n(9408),f=n(9683),x=n(3486),h=n(7767);function v(e){return"string"==typeof e?e:(0,c.formatUrl)(e)}let b=o.default.forwardRef(function(e,a){let n,t;let{href:c,as:b,children:g,prefetch:y=null,passHref:w,replace:_,shallow:E,scroll:k,locale:j,onClick:R,onMouseEnter:S,onTouchStart:O,legacyBehavior:A=!1,...C}=e;n=g,A&&("string"==typeof n||"number"==typeof n)&&(n=(0,i.jsx)("a",{children:n}));let T=o.default.useContext(u.RouterContext),P=o.default.useContext(d.AppRouterContext),z=null!=T?T:P,N=!T,F=!1!==y,L=null===y?h.PrefetchKind.AUTO:h.PrefetchKind.FULL,{href:I,as:U}=o.default.useMemo(()=>{if(!T){let e=v(c);return{href:e,as:b?v(b):e}}let[e,a]=(0,s.resolveHref)(T,c,!0);return{href:e,as:b?(0,s.resolveHref)(T,b):a||e}},[T,c,b]),B=o.default.useRef(I),D=o.default.useRef(U);A&&(t=o.default.Children.only(n));let M=A?t&&"object"==typeof t&&t.ref:a,[q,W,H]=(0,m.useIntersection)({rootMargin:"200px"}),G=o.default.useCallback(e=>{(D.current!==U||B.current!==I)&&(H(),D.current=U,B.current=I),q(e),M&&("function"==typeof M?M(e):"object"==typeof M&&(M.current=e))},[U,M,I,H,q]);o.default.useEffect(()=>{},[U,I,W,j,F,null==T?void 0:T.locale,z,N,L]);let $={ref:G,onClick(e){A||"function"!=typeof R||R(e),A&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),z&&!e.defaultPrevented&&function(e,a,n,t,i,s,c,p,l){let{nodeName:u}=e.currentTarget;if("A"===u.toUpperCase()&&(function(e){let a=e.currentTarget.getAttribute("target");return a&&"_self"!==a||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!l&&!(0,r.isLocalURL)(n)))return;e.preventDefault();let d=()=>{let e=null==c||c;"beforePopState"in a?a[i?"replace":"push"](n,t,{shallow:s,locale:p,scroll:e}):a[i?"replace":"push"](t||n,{scroll:e})};l?o.default.startTransition(d):d()}(e,z,I,U,_,E,k,j,N)},onMouseEnter(e){A||"function"!=typeof S||S(e),A&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e)},onTouchStart:function(e){A||"function"!=typeof O||O(e),A&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e)}};if((0,p.isAbsoluteUrl)(U))$.href=U;else if(!A||w||"a"===t.type&&!("href"in t.props)){let e=void 0!==j?j:null==T?void 0:T.locale,a=(null==T?void 0:T.isLocaleDomain)&&(0,f.getDomainLocale)(U,e,null==T?void 0:T.locales,null==T?void 0:T.domainLocales);$.href=a||(0,x.addBasePath)((0,l.addLocale)(U,e,null==T?void 0:T.defaultLocale))}return A?o.default.cloneElement(t,$):(0,i.jsx)("a",{...C,...$,children:n})});("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},956:(e,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{cancelIdleCallback:function(){return t},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let a=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-a))}})},1)},t="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},5619:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"resolveHref",{enumerable:!0,get:function(){return u}});let t=n(2149),i=n(3071),o=n(757),s=n(1348),r=n(3658),c=n(944),p=n(4903),l=n(1394);function u(e,a,n){let u;let d="string"==typeof a?a:(0,i.formatWithValidation)(a),m=d.match(/^[a-zA-Z]{1,}:\/\//),f=m?d.slice(m[0].length):d;if((f.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+d+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let a=(0,s.normalizeRepeatedSlashes)(f);d=(m?m[0]:"")+a}if(!(0,c.isLocalURL)(d))return n?[d]:d;try{u=new URL(d.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){u=new URL("/","http://n")}try{let e=new URL(d,u);e.pathname=(0,r.normalizePathTrailingSlash)(e.pathname);let a="";if((0,p.isDynamicRoute)(e.pathname)&&e.searchParams&&n){let n=(0,t.searchParamsToUrlQuery)(e.searchParams),{result:s,params:r}=(0,l.interpolateAs)(e.pathname,e.pathname,n);s&&(a=(0,i.formatWithValidation)({pathname:s,hash:e.hash,query:(0,o.omit)(n,r)}))}let s=e.origin===u.origin?e.href.slice(e.origin.length):e.href;return n?[s,a||s]:s}catch(e){return n?[d]:d}}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},9408:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"useIntersection",{enumerable:!0,get:function(){return c}});let t=n(7577),i=n(956),o="function"==typeof IntersectionObserver,s=new Map,r=[];function c(e){let{rootRef:a,rootMargin:n,disabled:c}=e,p=c||!o,[l,u]=(0,t.useState)(!1),d=(0,t.useRef)(null),m=(0,t.useCallback)(e=>{d.current=e},[]);return(0,t.useEffect)(()=>{if(o){if(p||l)return;let e=d.current;if(e&&e.tagName)return function(e,a,n){let{id:t,observer:i,elements:o}=function(e){let a;let n={root:e.root||null,margin:e.rootMargin||""},t=r.find(e=>e.root===n.root&&e.margin===n.margin);if(t&&(a=s.get(t)))return a;let i=new Map;return a={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let a=i.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;a&&n&&a(n)})},e),elements:i},r.push(n),s.set(n,a),a}(n);return o.set(e,a),i.observe(e),function(){if(o.delete(e),i.unobserve(e),0===o.size){i.disconnect(),s.delete(t);let e=r.findIndex(e=>e.root===t.root&&e.margin===t.margin);e>-1&&r.splice(e,1)}}}(e,e=>e&&u(e),{root:null==a?void 0:a.current,rootMargin:n})}else if(!l){let e=(0,i.requestIdleCallback)(()=>u(!0));return()=>(0,i.cancelIdleCallback)(e)}},[p,n,a,l,d.current]),[m,l,(0,t.useCallback)(()=>{u(!1)},[])]}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},5633:(e,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{ACTION_SUFFIX:function(){return c},APP_DIR_ALIAS:function(){return S},CACHE_ONE_YEAR:function(){return y},DOT_NEXT_ALIAS:function(){return j},ESLINT_DEFAULT_DIRS:function(){return G},GSP_NO_RETURNED_VALUE:function(){return B},GSSP_COMPONENT_MEMBER_ERROR:function(){return q},GSSP_NO_RETURNED_VALUE:function(){return D},INSTRUMENTATION_HOOK_FILENAME:function(){return E},MIDDLEWARE_FILENAME:function(){return w},MIDDLEWARE_LOCATION_REGEXP:function(){return _},NEXT_BODY_SUFFIX:function(){return u},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return g},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return f},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return x},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return m},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return b},NEXT_CACHE_TAGS_HEADER:function(){return d},NEXT_CACHE_TAG_MAX_ITEMS:function(){return h},NEXT_CACHE_TAG_MAX_LENGTH:function(){return v},NEXT_DATA_SUFFIX:function(){return p},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return t},NEXT_META_SUFFIX:function(){return l},NEXT_QUERY_PARAM_PREFIX:function(){return n},NON_STANDARD_NODE_ENV:function(){return W},PAGES_DIR_ALIAS:function(){return k},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return z},ROOT_DIR_ALIAS:function(){return R},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return P},RSC_ACTION_ENCRYPTION_ALIAS:function(){return T},RSC_ACTION_PROXY_ALIAS:function(){return C},RSC_ACTION_VALIDATE_ALIAS:function(){return A},RSC_MOD_REF_PROXY_ALIAS:function(){return O},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SUFFIX:function(){return r},SERVER_PROPS_EXPORT_ERROR:function(){return U},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return F},SERVER_PROPS_SSG_CONFLICT:function(){return L},SERVER_RUNTIME:function(){return $},SSG_FALLBACK_EXPORT_ERROR:function(){return H},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return N},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return I},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return M},WEBPACK_LAYERS:function(){return K},WEBPACK_RESOURCE_QUERIES:function(){return X}});let n="nxtP",t="nxtI",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",r=".rsc",c=".action",p=".json",l=".meta",u=".body",d="x-next-cache-tags",m="x-next-cache-soft-tags",f="x-next-revalidated-tags",x="x-next-revalidate-tag-token",h=128,v=256,b=1024,g="_N_T_",y=31536e3,w="middleware",_=`(?:src/)?${w}`,E="instrumentation",k="private-next-pages",j="private-dot-next",R="private-next-root-dir",S="private-next-app-dir",O="private-next-rsc-mod-ref-proxy",A="private-next-rsc-action-validate",C="private-next-rsc-server-reference",T="private-next-rsc-action-encryption",P="private-next-rsc-action-client-wrapper",z="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",N="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",F="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",L="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",I="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",U="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",B="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",D="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",M="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",q="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",W='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',H="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",G=["app","pages","components","lib","src"],$={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},V={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},K={...V,GROUP:{serverOnly:[V.reactServerComponents,V.actionBrowser,V.appMetadataRoute,V.appRouteHandler,V.instrument],clientOnly:[V.serverSideRendering,V.appPagesBrowser],nonClientServerTarget:[V.middleware,V.api],app:[V.reactServerComponents,V.actionBrowser,V.appMetadataRoute,V.appRouteHandler,V.serverSideRendering,V.appPagesBrowser,V.shared,V.instrument]}},X={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},131:(e,a,n)=>{"use strict";e.exports=n(1616).vendored.contexts.RouterContext},2451:(e,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,t=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(t,"\\$&"):e}},3071:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{formatUrl:function(){return o},formatWithValidation:function(){return r},urlObjectKeys:function(){return s}});let t=n(8374)._(n(2149)),i=/https?|ftp|gopher|file/;function o(e){let{auth:a,hostname:n}=e,o=e.protocol||"",s=e.pathname||"",r=e.hash||"",c=e.query||"",p=!1;a=a?encodeURIComponent(a).replace(/%3A/i,":")+"@":"",e.host?p=a+e.host:n&&(p=a+(~n.indexOf(":")?"["+n+"]":n),e.port&&(p+=":"+e.port)),c&&"object"==typeof c&&(c=String(t.urlQueryToSearchParams(c)));let l=e.search||c&&"?"+c||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==p?(p="//"+(p||""),s&&"/"!==s[0]&&(s="/"+s)):p||(p=""),r&&"#"!==r[0]&&(r="#"+r),l&&"?"!==l[0]&&(l="?"+l),""+o+p+(s=s.replace(/[?#]/g,encodeURIComponent))+(l=l.replace("#","%23"))+r}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function r(e){return o(e)}},4903:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{getSortedRoutes:function(){return t.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let t=n(4712),i=n(5541)},1394:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"interpolateAs",{enumerable:!0,get:function(){return o}});let t=n(9966),i=n(7249);function o(e,a,n){let o="",s=(0,i.getRouteRegex)(e),r=s.groups,c=(a!==e?(0,t.getRouteMatcher)(s)(a):"")||n;o=e;let p=Object.keys(r);return p.every(e=>{let a=c[e]||"",{repeat:n,optional:t}=r[e],i="["+(n?"...":"")+e+"]";return t&&(i=(a?"":"/")+"["+i+"]"),n&&!Array.isArray(a)&&(a=[a]),(t||e in c)&&(o=o.replace(i,n?a.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(a))||"/")})||(o=""),{params:p,result:o}}},5541:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let t=n(7356),i=/\/\[[^/]+?\](?=\/|$)/;function o(e){return(0,t.isInterceptionRouteAppPath)(e)&&(e=(0,t.extractInterceptionRouteInformation)(e).interceptedRoute),i.test(e)}},944:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"isLocalURL",{enumerable:!0,get:function(){return o}});let t=n(1348),i=n(7929);function o(e){if(!(0,t.isAbsoluteUrl)(e))return!0;try{let a=(0,t.getLocationOrigin)(),n=new URL(e,a);return n.origin===a&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},757:(e,a)=>{"use strict";function n(e,a){let n={};return Object.keys(e).forEach(t=>{a.includes(t)||(n[t]=e[t])}),n}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"omit",{enumerable:!0,get:function(){return n}})},2149:(e,a)=>{"use strict";function n(e){let a={};return e.forEach((e,n)=>{void 0===a[n]?a[n]=e:Array.isArray(a[n])?a[n].push(e):a[n]=[a[n],e]}),a}function t(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let a=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,i]=e;Array.isArray(i)?i.forEach(e=>a.append(n,t(e))):a.set(n,t(i))}),a}function o(e){for(var a=arguments.length,n=Array(a>1?a-1:0),t=1;t<a;t++)n[t-1]=arguments[t];return n.forEach(a=>{Array.from(a.keys()).forEach(a=>e.delete(a)),a.forEach((a,n)=>e.append(n,a))}),e}Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},9966:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let t=n(1348);function i(e){let{re:a,groups:n}=e;return e=>{let i=a.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw new t.DecodeError("failed to decode param")}},s={};return Object.keys(n).forEach(e=>{let a=n[e],t=i[a.pos];void 0!==t&&(s[e]=~t.indexOf("/")?t.split("/").map(e=>o(e)):a.repeat?[o(t)]:o(t))}),s}}},7249:(e,a,n)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return d},getRouteRegex:function(){return p},parseParameter:function(){return r}});let t=n(5633),i=n(7356),o=n(2451),s=n(3236);function r(e){let a=e.startsWith("[")&&e.endsWith("]");a&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:a}}function c(e){let a=(0,s.removeTrailingSlash)(e).slice(1).split("/"),n={},t=1;return{parameterizedRoute:a.map(e=>{let a=i.INTERCEPTION_ROUTE_MARKERS.find(a=>e.startsWith(a)),s=e.match(/\[((?:\[.*\])|.+)\]/);if(a&&s){let{key:e,optional:i,repeat:c}=r(s[1]);return n[e]={pos:t++,repeat:c,optional:i},"/"+(0,o.escapeStringRegexp)(a)+"([^/]+?)"}if(!s)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:a,optional:i}=r(s[1]);return n[e]={pos:t++,repeat:a,optional:i},a?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:n}}function p(e){let{parameterizedRoute:a,groups:n}=c(e);return{re:RegExp("^"+a+"(?:/)?$"),groups:n}}function l(e){let{interceptionMarker:a,getSafeRouteKey:n,segment:t,routeKeys:i,keyPrefix:s}=e,{key:c,optional:p,repeat:l}=r(t),u=c.replace(/\W/g,"");s&&(u=""+s+u);let d=!1;(0===u.length||u.length>30)&&(d=!0),isNaN(parseInt(u.slice(0,1)))||(d=!0),d&&(u=n()),s?i[u]=""+s+c:i[u]=c;let m=a?(0,o.escapeStringRegexp)(a):"";return l?p?"(?:/"+m+"(?<"+u+">.+?))?":"/"+m+"(?<"+u+">.+?)":"/"+m+"(?<"+u+">[^/]+?)"}function u(e,a){let n;let r=(0,s.removeTrailingSlash)(e).slice(1).split("/"),c=(n=0,()=>{let e="",a=++n;for(;a>0;)e+=String.fromCharCode(97+(a-1)%26),a=Math.floor((a-1)/26);return e}),p={};return{namedParameterizedRoute:r.map(e=>{let n=i.INTERCEPTION_ROUTE_MARKERS.some(a=>e.startsWith(a)),s=e.match(/\[((?:\[.*\])|.+)\]/);if(n&&s){let[n]=e.split(s[0]);return l({getSafeRouteKey:c,interceptionMarker:n,segment:s[1],routeKeys:p,keyPrefix:a?t.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return s?l({getSafeRouteKey:c,segment:s[1],routeKeys:p,keyPrefix:a?t.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:p}}function d(e,a){let n=u(e,a);return{...p(e),namedRegex:"^"+n.namedParameterizedRoute+"(?:/)?$",routeKeys:n.routeKeys}}function m(e,a){let{parameterizedRoute:n}=c(e),{catchAll:t=!0}=a;if("/"===n)return{namedRegex:"^/"+(t?".*":"")+"$"};let{namedParameterizedRoute:i}=u(e,!1);return{namedRegex:"^"+i+(t?"(?:(/.*)?)":"")+"$"}}},4712:(e,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"getSortedRoutes",{enumerable:!0,get:function(){return t}});class n{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let a=[...this.children.keys()].sort();null!==this.slugName&&a.splice(a.indexOf("[]"),1),null!==this.restSlugName&&a.splice(a.indexOf("[...]"),1),null!==this.optionalRestSlugName&&a.splice(a.indexOf("[[...]]"),1);let n=a.map(a=>this.children.get(a)._smoosh(""+e+a+"/")).reduce((e,a)=>[...e,...a],[]);if(null!==this.slugName&&n.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let a="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+a+'" and "'+a+"[[..."+this.optionalRestSlugName+']]").');n.unshift(a)}return null!==this.restSlugName&&n.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&n.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),n}_insert(e,a,t){if(0===e.length){this.placeholder=!1;return}if(t)throw Error("Catch-all must be the last part of the URL.");let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let n=i.slice(1,-1),s=!1;if(n.startsWith("[")&&n.endsWith("]")&&(n=n.slice(1,-1),s=!0),n.startsWith("...")&&(n=n.substring(3),t=!0),n.startsWith("[")||n.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+n+"').");if(n.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+n+"').");function o(e,n){if(null!==e&&e!==n)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+n+"').");a.forEach(e=>{if(e===n)throw Error('You cannot have the same slug name "'+n+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+n+'" differ only by non-word symbols within a single dynamic path')}),a.push(n)}if(t){if(s){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');o(this.optionalRestSlugName,n),this.optionalRestSlugName=n,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');o(this.restSlugName,n),this.restSlugName=n,i="[...]"}}else{if(s)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');o(this.slugName,n),this.slugName=n,i="[]"}}this.children.has(i)||this.children.set(i,new n),this.children.get(i)._insert(e.slice(1),a,t)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function t(e){let a=new n;return e.forEach(e=>a.insert(e)),a.smoosh()}},1348:(e,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var n in a)Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}(a,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return x},PageNotFoundError:function(){return h},SP:function(){return d},ST:function(){return m},WEB_VITALS:function(){return n},execOnce:function(){return t},getDisplayName:function(){return c},getLocationOrigin:function(){return s},getURL:function(){return r},isAbsoluteUrl:function(){return o},isResSent:function(){return p},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return g}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function t(e){let a,n=!1;return function(){for(var t=arguments.length,i=Array(t),o=0;o<t;o++)i[o]=arguments[o];return n||(n=!0,a=e(...i)),a}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:a,port:n}=window.location;return e+"//"+a+(n?":"+n:"")}function r(){let{href:e}=window.location,a=s();return e.substring(a.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function p(e){return e.finished||e.headersSent}function l(e){let a=e.split("?");return a[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(a[1]?"?"+a.slice(1).join("?"):"")}async function u(e,a){let n=a.res||a.ctx&&a.ctx.res;if(!e.getInitialProps)return a.ctx&&a.Component?{pageProps:await u(a.Component,a.ctx)}:{};let t=await e.getInitialProps(a);if(n&&p(n))return t;if(!t)throw Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+t+'" instead.');return t}let d="undefined"!=typeof performance,m=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class x extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,a){super(),this.message="Failed to load static file for page: "+e+" "+a}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function g(e){return JSON.stringify({message:e.message,stack:e.stack})}},3304:(e,a,n)=>{"use strict";var t=n(7360).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,n,r,c="string"==typeof e?t(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),n=u=parseInt(u)||i[p]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var t=e.match(/^(.+):(\d+)$/),i=t?t[1]:e,s=t?parseInt(t[2]):0;return!!s&&s!==n||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!o.call(a,i)):a!==i)}))))return"";var d=s("npm_config_"+p+"_proxy")||s(p+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},7495:(e,a,n)=>{"use strict";let t;let i=n(9801),o=n(4175),s=n(3285),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===t)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===t)return 0;let n=t||0;if("dumb"===r.TERM)return n;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:n;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:n}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?t=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(t=1),"FORCE_COLOR"in r&&(t="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,o.isatty(1))),stderr:c(p(!0,o.isatty(2)))}},5442:(e,a,n)=>{"use strict";var t=n(7577),i="function"==typeof Object.is?Object.is:function(e,a){return e===a&&(0!==e||1/e==1/a)||e!=e&&a!=a},o=t.useState,s=t.useEffect,r=t.useLayoutEffect,c=t.useDebugValue;function p(e){var a=e.getSnapshot;e=e.value;try{var n=a();return!i(e,n)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,a){return a()}:function(e,a){var n=a(),t=o({inst:{value:n,getSnapshot:a}}),i=t[0].inst,l=t[1];return r(function(){i.value=n,i.getSnapshot=a,p(i)&&l({inst:i})},[e,n,a]),s(function(){return p(i)&&l({inst:i}),e(function(){p(i)&&l({inst:i})})},[e]),c(n),n};a.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:l},9251:(e,a,n)=>{"use strict";var t=n(7577),i=n(4095),o="function"==typeof Object.is?Object.is:function(e,a){return e===a&&(0!==e||1/e==1/a)||e!=e&&a!=a},s=i.useSyncExternalStore,r=t.useRef,c=t.useEffect,p=t.useMemo,l=t.useDebugValue;a.useSyncExternalStoreWithSelector=function(e,a,n,t,i){var u=r(null);if(null===u.current){var d={hasValue:!1,value:null};u.current=d}else d=u.current;var m=s(e,(u=p(function(){function e(e){if(!c){if(c=!0,s=e,e=t(e),void 0!==i&&d.hasValue){var a=d.value;if(i(a,e))return r=a}return r=e}if(a=r,o(s,e))return a;var n=t(e);return void 0!==i&&i(a,n)?(s=e,a):(s=e,r=n)}var s,r,c=!1,p=void 0===n?null:n;return[function(){return e(a())},null===p?void 0:function(){return e(p())}]},[a,n,t,i]))[0],u[1]);return c(function(){d.hasValue=!0,d.value=m},[m]),l(m),m}},4095:(e,a,n)=>{"use strict";e.exports=n(5442)},1508:(e,a,n)=>{"use strict";e.exports=n(9251)},3351:(e,a,n)=>{"use strict";n.d(a,{Z:()=>i});var t=n(7577);let i=t.forwardRef(function({title:e,titleId:a,...n},i){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":a},n),e?t.createElement("title",{id:a},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},4099:(e,a,n)=>{"use strict";let t,i,o,s,r,c,p;n.d(a,{Z:()=>aQ});var l,u,d,m={};function f(e,a){return function(){return e.apply(a,arguments)}}n.r(m),n.d(m,{hasBrowserEnv:()=>ey,hasStandardBrowserEnv:()=>e_,hasStandardBrowserWebWorkerEnv:()=>eE,navigator:()=>ew,origin:()=>ek});let{toString:x}=Object.prototype,{getPrototypeOf:h}=Object,{iterator:v,toStringTag:b}=Symbol,g=(t=Object.create(null),e=>{let a=x.call(e);return t[a]||(t[a]=a.slice(8,-1).toLowerCase())}),y=e=>(e=e.toLowerCase(),a=>g(a)===e),w=e=>a=>typeof a===e,{isArray:_}=Array,E=w("undefined"),k=y("ArrayBuffer"),j=w("string"),R=w("function"),S=w("number"),O=e=>null!==e&&"object"==typeof e,A=e=>{if("object"!==g(e))return!1;let a=h(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(b in e)&&!(v in e)},C=y("Date"),T=y("File"),P=y("Blob"),z=y("FileList"),N=y("URLSearchParams"),[F,L,I,U]=["ReadableStream","Request","Response","Headers"].map(y);function B(e,a,{allOwnKeys:n=!1}={}){let t,i;if(null!=e){if("object"!=typeof e&&(e=[e]),_(e))for(t=0,i=e.length;t<i;t++)a.call(null,e[t],t,e);else{let i;let o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(t=0;t<s;t++)i=o[t],a.call(null,e[i],i,e)}}}function D(e,a){let n;a=a.toLowerCase();let t=Object.keys(e),i=t.length;for(;i-- >0;)if(a===(n=t[i]).toLowerCase())return n;return null}let M="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,q=e=>!E(e)&&e!==M,W=(i="undefined"!=typeof Uint8Array&&h(Uint8Array),e=>i&&e instanceof i),H=y("HTMLFormElement"),G=(({hasOwnProperty:e})=>(a,n)=>e.call(a,n))(Object.prototype),$=y("RegExp"),V=(e,a)=>{let n=Object.getOwnPropertyDescriptors(e),t={};B(n,(n,i)=>{let o;!1!==(o=a(n,i,e))&&(t[i]=o||n)}),Object.defineProperties(e,t)},K=y("AsyncFunction"),X=(l="function"==typeof setImmediate,u=R(M.postMessage),l?setImmediate:u?(c=`axios@${Math.random()}`,p=[],M.addEventListener("message",({source:e,data:a})=>{e===M&&a===c&&p.length&&p.shift()()},!1),e=>{p.push(e),M.postMessage(c,"*")}):e=>setTimeout(e)),J="undefined"!=typeof queueMicrotask?queueMicrotask.bind(M):"undefined"!=typeof process&&process.nextTick||X,Y={isArray:_,isArrayBuffer:k,isBuffer:function(e){return null!==e&&!E(e)&&null!==e.constructor&&!E(e.constructor)&&R(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||R(e.append)&&("formdata"===(a=g(e))||"object"===a&&R(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&k(e.buffer)},isString:j,isNumber:S,isBoolean:e=>!0===e||!1===e,isObject:O,isPlainObject:A,isReadableStream:F,isRequest:L,isResponse:I,isHeaders:U,isUndefined:E,isDate:C,isFile:T,isBlob:P,isRegExp:$,isFunction:R,isStream:e=>O(e)&&R(e.pipe),isURLSearchParams:N,isTypedArray:W,isFileList:z,forEach:B,merge:function e(){let{caseless:a}=q(this)&&this||{},n={},t=(t,i)=>{let o=a&&D(n,i)||i;A(n[o])&&A(t)?n[o]=e(n[o],t):A(t)?n[o]=e({},t):_(t)?n[o]=t.slice():n[o]=t};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&B(arguments[e],t);return n},extend:(e,a,n,{allOwnKeys:t}={})=>(B(a,(a,t)=>{n&&R(a)?e[t]=f(a,n):e[t]=a},{allOwnKeys:t}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,n,t)=>{e.prototype=Object.create(a.prototype,t),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,a,n,t)=>{let i,o,s;let r={};if(a=a||{},null==e)return a;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)s=i[o],(!t||t(s,e,a))&&!r[s]&&(a[s]=e[s],r[s]=!0);e=!1!==n&&h(e)}while(e&&(!n||n(e,a))&&e!==Object.prototype);return a},kindOf:g,kindOfTest:y,endsWith:(e,a,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=a.length;let t=e.indexOf(a,n);return -1!==t&&t===n},toArray:e=>{if(!e)return null;if(_(e))return e;let a=e.length;if(!S(a))return null;let n=Array(a);for(;a-- >0;)n[a]=e[a];return n},forEachEntry:(e,a)=>{let n;let t=(e&&e[v]).call(e);for(;(n=t.next())&&!n.done;){let t=n.value;a.call(e,t[0],t[1])}},matchAll:(e,a)=>{let n;let t=[];for(;null!==(n=e.exec(a));)t.push(n);return t},isHTMLForm:H,hasOwnProperty:G,hasOwnProp:G,reduceDescriptors:V,freezeMethods:e=>{V(e,(a,n)=>{if(R(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;if(R(e[n])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},toObjectSet:(e,a)=>{let n={};return(e=>{e.forEach(e=>{n[e]=!0})})(_(e)?e:String(e).split(a)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,n){return a.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:D,global:M,isContextDefined:q,isSpecCompliantForm:function(e){return!!(e&&R(e.append)&&"FormData"===e[b]&&e[v])},toJSONObject:e=>{let a=Array(10),n=(e,t)=>{if(O(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[t]=e;let i=_(e)?[]:{};return B(e,(e,a)=>{let o=n(e,t+1);E(o)||(i[a]=o)}),a[t]=void 0,i}}return e};return n(e,0)},isAsyncFn:K,isThenable:e=>e&&(O(e)||R(e))&&R(e.then)&&R(e.catch),setImmediate:X,asap:J,isIterable:e=>null!=e&&R(e[v])};function Q(e,a,n,t,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),n&&(this.config=n),t&&(this.request=t),i&&(this.response=i,this.status=i.status?i.status:null)}Y.inherits(Q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Y.toJSONObject(this.config),code:this.code,status:this.status}}});let Z=Q.prototype,ee={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ee[e]={value:e}}),Object.defineProperties(Q,ee),Object.defineProperty(Z,"isAxiosError",{value:!0}),Q.from=(e,a,n,t,i,o)=>{let s=Object.create(Z);return Y.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Q.call(s,e.message,a,n,t,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var ea=n(4127);function en(e){return Y.isPlainObject(e)||Y.isArray(e)}function et(e){return Y.endsWith(e,"[]")?e.slice(0,-2):e}function ei(e,a,n){return e?e.concat(a).map(function(e,a){return e=et(e),!n&&a?"["+e+"]":e}).join(n?".":""):a}let eo=Y.toFlatObject(Y,{},null,function(e){return/^is[A-Z]/.test(e)}),es=function(e,a,n){if(!Y.isObject(e))throw TypeError("target must be an object");a=a||new(ea||FormData);let t=(n=Y.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!Y.isUndefined(a[e])})).metaTokens,i=n.visitor||p,o=n.dots,s=n.indexes,r=(n.Blob||"undefined"!=typeof Blob&&Blob)&&Y.isSpecCompliantForm(a);if(!Y.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(Y.isDate(e))return e.toISOString();if(!r&&Y.isBlob(e))throw new Q("Blob is not supported. Use a Buffer instead.");return Y.isArrayBuffer(e)||Y.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,n,i){let r=e;if(e&&!i&&"object"==typeof e){if(Y.endsWith(n,"{}"))n=t?n:n.slice(0,-2),e=JSON.stringify(e);else{var p;if(Y.isArray(e)&&(p=e,Y.isArray(p)&&!p.some(en))||(Y.isFileList(e)||Y.endsWith(n,"[]"))&&(r=Y.toArray(e)))return n=et(n),r.forEach(function(e,t){Y.isUndefined(e)||null===e||a.append(!0===s?ei([n],t,o):null===s?n:n+"[]",c(e))}),!1}}return!!en(e)||(a.append(ei(i,n,o),c(e)),!1)}let l=[],u=Object.assign(eo,{defaultVisitor:p,convertValue:c,isVisitable:en});if(!Y.isObject(e))throw TypeError("data must be an object");return function e(n,t){if(!Y.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+t.join("."));l.push(n),Y.forEach(n,function(n,o){!0===(!(Y.isUndefined(n)||null===n)&&i.call(a,n,Y.isString(o)?o.trim():o,t,u))&&e(n,t?t.concat(o):[o])}),l.pop()}}(e),a};function er(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function ec(e,a){this._pairs=[],e&&es(e,this,a)}let ep=ec.prototype;function el(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eu(e,a,n){let t;if(!a)return e;let i=n&&n.encode||el;Y.isFunction(n)&&(n={serialize:n});let o=n&&n.serialize;if(t=o?o(a,n):Y.isURLSearchParams(a)?a.toString():new ec(a,n).toString(i)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+t}return e}ep.append=function(e,a){this._pairs.push([e,a])},ep.toString=function(e){let a=e?function(a){return e.call(this,a,er)}:er;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class ed{constructor(){this.handlers=[]}use(e,a,n){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Y.forEach(this.handlers,function(a){null!==a&&e(a)})}}let em={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ef=n(4770);let ex=n(7360).URLSearchParams,eh="abcdefghijklmnopqrstuvwxyz",ev="0123456789",eb={DIGIT:ev,ALPHA:eh,ALPHA_DIGIT:eh+eh.toUpperCase()+ev},eg={isNode:!0,classes:{URLSearchParams:ex,FormData:ea,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:eb,generateString:(e=16,a=eb.ALPHA_DIGIT)=>{let n="",{length:t}=a,i=new Uint32Array(e);ef.randomFillSync(i);for(let o=0;o<e;o++)n+=a[i[o]%t];return n},protocols:["http","https","file","data"]},ey="undefined"!=typeof window&&"undefined"!=typeof document,ew="object"==typeof navigator&&navigator||void 0,e_=ey&&(!ew||0>["ReactNative","NativeScript","NS"].indexOf(ew.product)),eE="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ek=ey&&window.location.href||"http://localhost",ej={...m,...eg},eR=function(e){if(Y.isFormData(e)&&Y.isFunction(e.entries)){let a={};return Y.forEachEntry(e,(e,n)=>{!function e(a,n,t,i){let o=a[i++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=i>=a.length;return(o=!o&&Y.isArray(t)?t.length:o,r)?Y.hasOwnProp(t,o)?t[o]=[t[o],n]:t[o]=n:(t[o]&&Y.isObject(t[o])||(t[o]=[]),e(a,n,t[o],i)&&Y.isArray(t[o])&&(t[o]=function(e){let a,n;let t={},i=Object.keys(e),o=i.length;for(a=0;a<o;a++)t[n=i[a]]=e[n];return t}(t[o]))),!s}(Y.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),n,a,0)}),a}return null},eS={transitional:em,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let n;let t=a.getContentType()||"",i=t.indexOf("application/json")>-1,o=Y.isObject(e);if(o&&Y.isHTMLForm(e)&&(e=new FormData(e)),Y.isFormData(e))return i?JSON.stringify(eR(e)):e;if(Y.isArrayBuffer(e)||Y.isBuffer(e)||Y.isStream(e)||Y.isFile(e)||Y.isBlob(e)||Y.isReadableStream(e))return e;if(Y.isArrayBufferView(e))return e.buffer;if(Y.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(t.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,es(s,new ej.classes.URLSearchParams,Object.assign({visitor:function(e,a,n,t){return ej.isNode&&Y.isBuffer(e)?(this.append(a,e.toString("base64")),!1):t.defaultVisitor.apply(this,arguments)}},r))).toString()}if((n=Y.isFileList(e))||t.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return es(n?{"files[]":e}:e,a&&new a,this.formSerializer)}}return o||i?(a.setContentType("application/json",!1),function(e,a,n){if(Y.isString(e))try{return(0,JSON.parse)(e),Y.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eS.transitional,n=a&&a.forcedJSONParsing,t="json"===this.responseType;if(Y.isResponse(e)||Y.isReadableStream(e))return e;if(e&&Y.isString(e)&&(n&&!this.responseType||t)){let n=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!n&&t){if("SyntaxError"===e.name)throw Q.from(e,Q.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ej.classes.FormData,Blob:ej.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Y.forEach(["delete","get","head","post","put","patch"],e=>{eS.headers[e]={}});let eO=Y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eA=e=>{let a,n,t;let i={};return e&&e.split("\n").forEach(function(e){t=e.indexOf(":"),a=e.substring(0,t).trim().toLowerCase(),n=e.substring(t+1).trim(),!a||i[a]&&eO[a]||("set-cookie"===a?i[a]?i[a].push(n):i[a]=[n]:i[a]=i[a]?i[a]+", "+n:n)}),i},eC=Symbol("internals");function eT(e){return e&&String(e).trim().toLowerCase()}function eP(e){return!1===e||null==e?e:Y.isArray(e)?e.map(eP):String(e)}let ez=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eN(e,a,n,t,i){if(Y.isFunction(t))return t.call(this,a,n);if(i&&(a=n),Y.isString(a)){if(Y.isString(t))return -1!==a.indexOf(t);if(Y.isRegExp(t))return t.test(a)}}class eF{constructor(e){e&&this.set(e)}set(e,a,n){let t=this;function i(e,a,n){let i=eT(a);if(!i)throw Error("header name must be a non-empty string");let o=Y.findKey(t,i);o&&void 0!==t[o]&&!0!==n&&(void 0!==n||!1===t[o])||(t[o||a]=eP(e))}let o=(e,a)=>Y.forEach(e,(e,n)=>i(e,n,a));if(Y.isPlainObject(e)||e instanceof this.constructor)o(e,a);else if(Y.isString(e)&&(e=e.trim())&&!ez(e))o(eA(e),a);else if(Y.isObject(e)&&Y.isIterable(e)){let n={},t,i;for(let a of e){if(!Y.isArray(a))throw TypeError("Object iterator must return a key-value pair");n[i=a[0]]=(t=n[i])?Y.isArray(t)?[...t,a[1]]:[t,a[1]]:a[1]}o(n,a)}else null!=e&&i(a,e,n);return this}get(e,a){if(e=eT(e)){let n=Y.findKey(this,e);if(n){let e=this[n];if(!a)return e;if(!0===a)return function(e){let a;let n=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=t.exec(e);)n[a[1]]=a[2];return n}(e);if(Y.isFunction(a))return a.call(this,e,n);if(Y.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eT(e)){let n=Y.findKey(this,e);return!!(n&&void 0!==this[n]&&(!a||eN(this,this[n],n,a)))}return!1}delete(e,a){let n=this,t=!1;function i(e){if(e=eT(e)){let i=Y.findKey(n,e);i&&(!a||eN(n,n[i],i,a))&&(delete n[i],t=!0)}}return Y.isArray(e)?e.forEach(i):i(e),t}clear(e){let a=Object.keys(this),n=a.length,t=!1;for(;n--;){let i=a[n];(!e||eN(this,this[i],i,e,!0))&&(delete this[i],t=!0)}return t}normalize(e){let a=this,n={};return Y.forEach(this,(t,i)=>{let o=Y.findKey(n,i);if(o){a[o]=eP(t),delete a[i];return}let s=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,n)=>a.toUpperCase()+n):String(i).trim();s!==i&&delete a[i],a[s]=eP(t),n[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return Y.forEach(this,(n,t)=>{null!=n&&!1!==n&&(a[t]=e&&Y.isArray(n)?n.join(", "):n)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let n=new this(e);return a.forEach(e=>n.set(e)),n}static accessor(e){let a=(this[eC]=this[eC]={accessors:{}}).accessors,n=this.prototype;function t(e){let t=eT(e);a[t]||(function(e,a){let n=Y.toCamelCase(" "+a);["get","set","has"].forEach(t=>{Object.defineProperty(e,t+n,{value:function(e,n,i){return this[t].call(this,a,e,n,i)},configurable:!0})})}(n,e),a[t]=!0)}return Y.isArray(e)?e.forEach(t):t(e),this}}function eL(e,a){let n=this||eS,t=a||n,i=eF.from(t.headers),o=t.data;return Y.forEach(e,function(e){o=e.call(n,o,i.normalize(),a?a.status:void 0)}),i.normalize(),o}function eI(e){return!!(e&&e.__CANCEL__)}function eU(e,a,n){Q.call(this,null==e?"canceled":e,Q.ERR_CANCELED,a,n),this.name="CanceledError"}function eB(e,a,n){let t=n.config.validateStatus;!n.status||!t||t(n.status)?e(n):a(new Q("Request failed with status code "+n.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function eD(e,a,n){let t=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&(t||!1==n)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}eF.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Y.reduceDescriptors(eF.prototype,({value:e},a)=>{let n=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[n]=e}}}),Y.freezeMethods(eF),Y.inherits(eU,Q,{__CANCEL__:!0});var eM=n(3304),eq=n(2615),eW=n(8791),eH=n(1764),eG=n(9831),e$=n(1568);let eV="1.9.0";function eK(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eX=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eJ=n(6162);let eY=Symbol("internals");class eQ extends eJ.Transform{constructor(e){super({readableHighWaterMark:(e=Y.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!Y.isUndefined(a[e]))).chunkSize});let a=this[eY]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[eY];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,n){let t=this[eY],i=t.maxRate,o=this.readableHighWaterMark,s=t.timeWindow,r=i/(1e3/s),c=!1!==t.minChunkSize?Math.max(t.minChunkSize,.01*r):0,p=(e,a)=>{let n=Buffer.byteLength(e);t.bytesSeen+=n,t.bytes+=n,t.isCaptured&&this.emit("progress",t.bytesSeen),this.push(e)?process.nextTick(a):t.onReadCallback=()=>{t.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let n;let l=Buffer.byteLength(e),u=null,d=o,m=0;if(i){let e=Date.now();(!t.ts||(m=e-t.ts)>=s)&&(t.ts=e,n=r-t.bytes,t.bytes=n<0?-n:0,m=0),n=r-t.bytes}if(i){if(n<=0)return setTimeout(()=>{a(null,e)},s-m);n<d&&(d=n)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,t){if(a)return n(a);t?l(t,e):n(null)})}}var eZ=n(7702);let{asyncIterator:e0}=Symbol,e1=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[e0]?yield*e[e0]():yield e},e3=ej.ALPHABET.ALPHA_DIGIT+"-_",e2="function"==typeof TextEncoder?new TextEncoder:new eH.TextEncoder,e4=e2.encode("\r\n");class e6{constructor(e,a){let{escapeName:n}=this.constructor,t=Y.isString(a),i=`Content-Disposition: form-data; name="${n(e)}"${!t&&a.name?`; filename="${n(a.name)}"`:""}\r
`;t?a=e2.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=e2.encode(i+"\r\n"),this.contentLength=t?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;Y.isTypedArray(e)?yield e:yield*e1(e),yield e4}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e5=(e,a,n)=>{let{tag:t="form-data-boundary",size:i=25,boundary:o=t+"-"+ej.generateString(i,e3)}=n||{};if(!Y.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=e2.encode("--"+o+"\r\n"),r=e2.encode("--"+o+"--\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let n=new e6(e,a);return c+=n.size,n});c+=s.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=Y.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),eJ.Readable.from(async function*(){for(let e of p)yield s,yield*e.encode();yield r}())};class e7 extends eJ.Transform{__transform(e,a,n){this.push(e),n()}_transform(e,a,n){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,n)}}let e8=(e,a)=>Y.isAsyncFn(e)?function(...n){let t=n.pop();e.apply(this,n).then(e=>{try{a?t(null,...a(e)):t(null,e)}catch(e){t(e)}},t)}:e,e9=function(e,a){let n;let t=Array(e=e||10),i=Array(e),o=0,s=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=i[s];n||(n=c),t[o]=r,i[o]=c;let l=s,u=0;for(;l!==o;)u+=t[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),c-n<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}},ae=function(e,a){let n,t,i=0,o=1e3/a,s=(a,o=Date.now())=>{i=o,n=null,t&&(clearTimeout(t),t=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-i;r>=o?s(e,a):(n=e,t||(t=setTimeout(()=>{t=null,s(n)},o-r)))},()=>n&&s(n)]},aa=(e,a,n=3)=>{let t=0,i=e9(50,250);return ae(n=>{let o=n.loaded,s=n.lengthComputable?n.total:void 0,r=o-t,c=i(r);t=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:n,lengthComputable:null!=s,[a?"download":"upload"]:!0})},n)},an=(e,a)=>{let n=null!=e;return[t=>a[0]({lengthComputable:n,total:e,loaded:t}),a[1]]},at=e=>(...a)=>Y.asap(()=>e(...a)),ai={flush:e$.constants.Z_SYNC_FLUSH,finishFlush:e$.constants.Z_SYNC_FLUSH},ao={flush:e$.constants.BROTLI_OPERATION_FLUSH,finishFlush:e$.constants.BROTLI_OPERATION_FLUSH},as=Y.isFunction(e$.createBrotliDecompress),{http:ar,https:ac}=eG,ap=/https:?/,al=ej.protocols.map(e=>e+":"),au=(e,[a,n])=>(e.on("end",n).on("error",n),a);function ad(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let am="undefined"!=typeof process&&"process"===Y.kindOf(process),af=e=>new Promise((a,n)=>{let t,i;let o=(e,a)=>{!i&&(i=!0,t&&t(e,a))},s=e=>{o(e,!0),n(e)};e(e=>{o(e),a(e)},s,e=>t=e).catch(s)}),ax=({address:e,family:a})=>{if(!Y.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},ah=(e,a)=>ax(Y.isObject(e)?e:{address:e,family:a}),av=am&&function(e){return af(async function(a,n,t){let i,o,s,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:x}=e,h=e.method.toUpperCase(),v=!1;if(d){let e=e8(d,e=>Y.isArray(e)?e:[e]);d=(a,n,t)=>{e(a,n,(e,a,i)=>{if(e)return t(e);let o=Y.isArray(a)?a.map(e=>ah(e)):[ah(a,i)];n.all?t(e,o):t(e,o[0].address,o[0].family)})}}let b=new eZ.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new eU(null,e,c):a)}t((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",n),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eD(e.baseURL,e.url,e.allowAbsoluteUrls),ej.hasBrowserEnv?ej.origin:void 0),_=w.protocol||al[0];if("data:"===_){let t;if("GET"!==h)return eB(a,n,{status:405,statusText:"method not allowed",headers:{},config:e});try{t=function(e,a,n){let t=n&&n.Blob||ej.classes.Blob,i=eK(e);if(void 0===a&&t&&(a=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let n=eX.exec(e);if(!n)throw new Q("Invalid URL",Q.ERR_INVALID_URL);let o=n[1],s=n[2],r=n[3],c=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(a){if(!t)throw new Q("Blob is not supported",Q.ERR_NOT_SUPPORT);return new t([c],{type:o})}return c}throw new Q("Unsupported protocol "+i,Q.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw Q.from(a,Q.ERR_BAD_REQUEST,e)}return"text"===f?(t=t.toString(x),x&&"utf8"!==x||(t=Y.stripBOM(t))):"stream"===f&&(t=eJ.Readable.from(t)),eB(a,n,{data:t,status:200,statusText:"OK",headers:new eF,config:e})}if(-1===al.indexOf(_))return n(new Q("Unsupported protocol "+_,Q.ERR_BAD_REQUEST,e));let E=eF.from(e.headers).normalize();E.set("User-Agent","axios/"+eV,!1);let{onUploadProgress:k,onDownloadProgress:j}=e,R=e.maxRate;if(Y.isSpecCompliantForm(u)){let e=E.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e5(u,e=>{E.set(e)},{tag:`axios-${eV}-boundary`,boundary:e&&e[1]||void 0})}else if(Y.isFormData(u)&&Y.isFunction(u.getHeaders)){if(E.set(u.getHeaders()),!E.hasContentLength())try{let e=await eH.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&E.setContentLength(e)}catch(e){}}else if(Y.isBlob(u)||Y.isFile(u))u.size&&E.setContentType(u.type||"application/octet-stream"),E.setContentLength(u.size||0),u=eJ.Readable.from(e1(u));else if(u&&!Y.isStream(u)){if(Buffer.isBuffer(u));else if(Y.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!Y.isString(u))return n(new Q("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",Q.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(E.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return n(new Q("Request body larger than maxBodyLength limit",Q.ERR_BAD_REQUEST,e))}let S=Y.toFiniteNumber(E.getContentLength());Y.isArray(R)?(i=R[0],o=R[1]):i=o=R,u&&(k||i)&&(Y.isStream(u)||(u=eJ.Readable.from(u,{objectMode:!1})),u=eJ.pipeline([u,new eQ({maxRate:Y.toFiniteNumber(i)})],Y.noop),k&&u.on("progress",au(u,an(S,aa(at(k),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&E.delete("authorization");try{p=eu(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(t){let a=Error(t.message);return a.config=e,a.url=e.url,a.exists=!0,n(a)}E.set("Accept-Encoding","gzip, compress, deflate"+(as?", br":""),!1);let O={path:p,method:h,headers:E.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:_,family:m,beforeRedirect:ad,beforeRedirects:{}};Y.isUndefined(d)||(O.lookup=d),e.socketPath?O.socketPath=e.socketPath:(O.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,O.port=w.port,function e(a,n,t){let i=n;if(!i&&!1!==i){let e=eM.getProxyForUrl(t);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=i.hostname||i.host;a.hostname=e,a.host=e,a.port=i.port,a.path=t,i.protocol&&(a.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,n,a.href)}}(O,e.proxy,_+"//"+w.hostname+(w.port?":"+w.port:"")+O.path));let A=ap.test(O.protocol);if(O.agent=A?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=A?eW:eq:(e.maxRedirects&&(O.maxRedirects=e.maxRedirects),e.beforeRedirect&&(O.beforeRedirects.config=e.beforeRedirect),l=A?ac:ar),e.maxBodyLength>-1?O.maxBodyLength=e.maxBodyLength:O.maxBodyLength=1/0,e.insecureHTTPParser&&(O.insecureHTTPParser=e.insecureHTTPParser),c=l.request(O,function(t){if(c.destroyed)return;let i=[t],s=+t.headers["content-length"];if(j||o){let e=new eQ({maxRate:Y.toFiniteNumber(o)});j&&e.on("progress",au(e,an(s,aa(at(j),!0,3)))),i.push(e)}let r=t,p=t.req||c;if(!1!==e.decompress&&t.headers["content-encoding"])switch(("HEAD"===h||204===t.statusCode)&&delete t.headers["content-encoding"],(t.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(e$.createUnzip(ai)),delete t.headers["content-encoding"];break;case"deflate":i.push(new e7),i.push(e$.createUnzip(ai)),delete t.headers["content-encoding"];break;case"br":as&&(i.push(e$.createBrotliDecompress(ao)),delete t.headers["content-encoding"])}r=i.length>1?eJ.pipeline(i,Y.noop):i[0];let l=eJ.finished(r,()=>{l(),g()}),u={status:t.statusCode,statusText:t.statusMessage,headers:new eF(t.headers),config:e,request:p};if("stream"===f)u.data=r,eB(a,n,u);else{let t=[],i=0;r.on("data",function(a){t.push(a),i+=a.length,e.maxContentLength>-1&&i>e.maxContentLength&&(v=!0,r.destroy(),n(new Q("maxContentLength size of "+e.maxContentLength+" exceeded",Q.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(v)return;let a=new Q("stream has been aborted",Q.ERR_BAD_RESPONSE,e,p);r.destroy(a),n(a)}),r.on("error",function(a){c.destroyed||n(Q.from(a,null,e,p))}),r.on("end",function(){try{let e=1===t.length?t[0]:Buffer.concat(t);"arraybuffer"===f||(e=e.toString(x),x&&"utf8"!==x||(e=Y.stripBOM(e))),u.data=e}catch(a){return n(Q.from(a,null,e,u.request,u))}eB(a,n,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{n(e),c.destroy(e)}),c.on("error",function(a){n(Q.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){n(new Q("error trying to parse `config.timeout` to int",Q.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",t=e.transitional||em;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),n(new Q(a,t.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,c)),y()})}if(Y.isStream(u)){let a=!1,n=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{n=!0,c.destroy(e)}),u.on("close",()=>{a||n||y(new eU("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},ab=ej.hasStandardBrowserEnv?(o=new URL(ej.origin),s=ej.navigator&&/(msie|trident)/i.test(ej.navigator.userAgent),e=>(e=new URL(e,ej.origin),o.protocol===e.protocol&&o.host===e.host&&(s||o.port===e.port))):()=>!0,ag=ej.hasStandardBrowserEnv?{write(e,a,n,t,i,o){let s=[e+"="+encodeURIComponent(a)];Y.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),Y.isString(t)&&s.push("path="+t),Y.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},ay=e=>e instanceof eF?{...e}:e;function aw(e,a){a=a||{};let n={};function t(e,a,n,t){return Y.isPlainObject(e)&&Y.isPlainObject(a)?Y.merge.call({caseless:t},e,a):Y.isPlainObject(a)?Y.merge({},a):Y.isArray(a)?a.slice():a}function i(e,a,n,i){return Y.isUndefined(a)?Y.isUndefined(e)?void 0:t(void 0,e,n,i):t(e,a,n,i)}function o(e,a){if(!Y.isUndefined(a))return t(void 0,a)}function s(e,a){return Y.isUndefined(a)?Y.isUndefined(e)?void 0:t(void 0,e):t(void 0,a)}function r(n,i,o){return o in a?t(n,i):o in e?t(void 0,n):void 0}let c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,a,n)=>i(ay(e),ay(a),n,!0)};return Y.forEach(Object.keys(Object.assign({},e,a)),function(t){let o=c[t]||i,s=o(e[t],a[t],t);Y.isUndefined(s)&&o!==r||(n[t]=s)}),n}let a_=e=>{let a;let n=aw({},e),{data:t,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:c}=n;if(n.headers=r=eF.from(r),n.url=eu(eD(n.baseURL,n.url,n.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),Y.isFormData(t)){if(ej.hasStandardBrowserEnv||ej.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...n]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...n].join("; "))}}if(ej.hasStandardBrowserEnv&&(i&&Y.isFunction(i)&&(i=i(n)),i||!1!==i&&ab(n.url))){let e=o&&s&&ag.read(s);e&&r.set(o,e)}return n},aE="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,n){let t,i,o,s,r;let c=a_(e),p=c.data,l=eF.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){s&&s(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(t),c.signal&&c.signal.removeEventListener("abort",t)}let x=new XMLHttpRequest;function h(){if(!x)return;let t=eF.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders());eB(function(e){a(e),f()},function(e){n(e),f()},{data:u&&"text"!==u&&"json"!==u?x.response:x.responseText,status:x.status,statusText:x.statusText,headers:t,config:e,request:x}),x=null}x.open(c.method.toUpperCase(),c.url,!0),x.timeout=c.timeout,"onloadend"in x?x.onloadend=h:x.onreadystatechange=function(){x&&4===x.readyState&&(0!==x.status||x.responseURL&&0===x.responseURL.indexOf("file:"))&&setTimeout(h)},x.onabort=function(){x&&(n(new Q("Request aborted",Q.ECONNABORTED,e,x)),x=null)},x.onerror=function(){n(new Q("Network Error",Q.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",t=c.transitional||em;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),n(new Q(a,t.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,x)),x=null},void 0===p&&l.setContentType(null),"setRequestHeader"in x&&Y.forEach(l.toJSON(),function(e,a){x.setRequestHeader(a,e)}),Y.isUndefined(c.withCredentials)||(x.withCredentials=!!c.withCredentials),u&&"json"!==u&&(x.responseType=c.responseType),m&&([o,r]=aa(m,!0),x.addEventListener("progress",o)),d&&x.upload&&([i,s]=aa(d),x.upload.addEventListener("progress",i),x.upload.addEventListener("loadend",s)),(c.cancelToken||c.signal)&&(t=a=>{x&&(n(!a||a.type?new eU(null,e,x):a),x.abort(),x=null)},c.cancelToken&&c.cancelToken.subscribe(t),c.signal&&(c.signal.aborted?t():c.signal.addEventListener("abort",t)));let v=eK(c.url);if(v&&-1===ej.protocols.indexOf(v)){n(new Q("Unsupported protocol "+v+":",Q.ERR_BAD_REQUEST,e));return}x.send(p||null)})},ak=(e,a)=>{let{length:n}=e=e?e.filter(Boolean):[];if(a||n){let n,t=new AbortController,i=function(e){if(!n){n=!0,s();let a=e instanceof Error?e:this.reason;t.abort(a instanceof Q?a:new eU(a instanceof Error?a.message:a))}},o=a&&setTimeout(()=>{o=null,i(new Q(`timeout ${a} of ms exceeded`,Q.ETIMEDOUT))},a),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:r}=t;return r.unsubscribe=()=>Y.asap(s),r}},aj=function*(e,a){let n,t=e.byteLength;if(!a||t<a){yield e;return}let i=0;for(;i<t;)n=i+a,yield e.slice(i,n),i=n},aR=async function*(e,a){for await(let n of aS(e))yield*aj(n,a)},aS=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:n}=await a.read();if(e)break;yield n}}finally{await a.cancel()}},aO=(e,a,n,t)=>{let i;let o=aR(e,a),s=0,r=e=>{!i&&(i=!0,t&&t(e))};return new ReadableStream({async pull(e){try{let{done:a,value:t}=await o.next();if(a){r(),e.close();return}let i=t.byteLength;if(n){let e=s+=i;n(e)}e.enqueue(new Uint8Array(t))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},aA="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aC=aA&&"function"==typeof ReadableStream,aT=aA&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aP=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},az=aC&&aP(()=>{let e=!1,a=new Request(ej.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aN=aC&&aP(()=>Y.isReadableStream(new Response("").body)),aF={stream:aN&&(e=>e.body)};aA&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{aF[e]||(aF[e]=Y.isFunction(d[e])?a=>a[e]():(a,n)=>{throw new Q(`Response type '${e}' is not supported`,Q.ERR_NOT_SUPPORT,n)})}));let aL=async e=>{if(null==e)return 0;if(Y.isBlob(e))return e.size;if(Y.isSpecCompliantForm(e)){let a=new Request(ej.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return Y.isArrayBufferView(e)||Y.isArrayBuffer(e)?e.byteLength:(Y.isURLSearchParams(e)&&(e+=""),Y.isString(e))?(await aT(e)).byteLength:void 0},aI=async(e,a)=>{let n=Y.toFiniteNumber(e.getContentLength());return null==n?aL(a):n},aU={http:av,xhr:aE,fetch:aA&&(async e=>{let a,n,{url:t,method:i,data:o,signal:s,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=a_(e);u=u?(u+"").toLowerCase():"text";let x=ak([s,r&&r.toAbortSignal()],c),h=x&&x.unsubscribe&&(()=>{x.unsubscribe()});try{if(l&&az&&"get"!==i&&"head"!==i&&0!==(n=await aI(d,o))){let e,a=new Request(t,{method:"POST",body:o,duplex:"half"});if(Y.isFormData(o)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,t]=an(n,aa(at(l)));o=aO(a.body,65536,e,t)}}Y.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;a=new Request(t,{...f,signal:x,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(a),c=aN&&("stream"===u||"response"===u);if(aN&&(p||c&&h)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=Y.toFiniteNumber(r.headers.get("content-length")),[n,t]=p&&an(a,aa(at(p),!0))||[];r=new Response(aO(r.body,65536,n,()=>{t&&t(),h&&h()}),e)}u=u||"text";let v=await aF[Y.findKey(aF,u)||"text"](r,e);return!c&&h&&h(),await new Promise((n,t)=>{eB(n,t,{data:v,headers:eF.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(n){if(h&&h(),n&&"TypeError"===n.name&&/Load failed|fetch/i.test(n.message))throw Object.assign(new Q("Network Error",Q.ERR_NETWORK,e,a),{cause:n.cause||n});throw Q.from(n,n&&n.code,e,a)}})};Y.forEach(aU,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aB=e=>`- ${e}`,aD=e=>Y.isFunction(e)||null===e||!1===e,aM={getAdapter:e=>{let a,n;let{length:t}=e=Y.isArray(e)?e:[e],i={};for(let o=0;o<t;o++){let t;if(n=a=e[o],!aD(a)&&void 0===(n=aU[(t=String(a)).toLowerCase()]))throw new Q(`Unknown adapter '${t}'`);if(n)break;i[t||"#"+o]=n}if(!n){let e=Object.entries(i).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new Q("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(aB).join("\n"):" "+aB(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return n}};function aq(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eU(null,e)}function aW(e){return aq(e),e.headers=eF.from(e.headers),e.data=eL.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aM.getAdapter(e.adapter||eS.adapter)(e).then(function(a){return aq(e),a.data=eL.call(e,e.transformResponse,a),a.headers=eF.from(a.headers),a},function(a){return!eI(a)&&(aq(e),a&&a.response&&(a.response.data=eL.call(e,e.transformResponse,a.response),a.response.headers=eF.from(a.response.headers))),Promise.reject(a)})}let aH={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aH[e]=function(n){return typeof n===e||"a"+(a<1?"n ":" ")+e}});let aG={};aH.transitional=function(e,a,n){function t(e,a){return"[Axios v"+eV+"] Transitional option '"+e+"'"+a+(n?". "+n:"")}return(n,i,o)=>{if(!1===e)throw new Q(t(i," has been removed"+(a?" in "+a:"")),Q.ERR_DEPRECATED);return a&&!aG[i]&&(aG[i]=!0,console.warn(t(i," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(n,i,o)}},aH.spelling=function(e){return(a,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};let a$={assertOptions:function(e,a,n){if("object"!=typeof e)throw new Q("options must be an object",Q.ERR_BAD_OPTION_VALUE);let t=Object.keys(e),i=t.length;for(;i-- >0;){let o=t[i],s=a[o];if(s){let a=e[o],n=void 0===a||s(a,o,e);if(!0!==n)throw new Q("option "+o+" must be "+n,Q.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Q("Unknown option "+o,Q.ERR_BAD_OPTION)}},validators:aH},aV=a$.validators;class aK{constructor(e){this.defaults=e||{},this.interceptors={request:new ed,response:new ed}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let n=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,a){let n,t;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:i,paramsSerializer:o,headers:s}=a=aw(this.defaults,a);void 0!==i&&a$.assertOptions(i,{silentJSONParsing:aV.transitional(aV.boolean),forcedJSONParsing:aV.transitional(aV.boolean),clarifyTimeoutError:aV.transitional(aV.boolean)},!1),null!=o&&(Y.isFunction(o)?a.paramsSerializer={serialize:o}:a$.assertOptions(o,{encode:aV.function,serialize:aV.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),a$.assertOptions(a,{baseUrl:aV.spelling("baseURL"),withXsrfToken:aV.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=s&&Y.merge(s.common,s[a.method]);s&&Y.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),a.headers=eF.concat(r,s);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[aW.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),t=e.length,n=Promise.resolve(a);u<t;)n=n.then(e[u++],e[u++]);return n}t=c.length;let d=a;for(u=0;u<t;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{n=aW.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,t=l.length;u<t;)n=n.then(l[u++],l[u++]);return n}getUri(e){return eu(eD((e=aw(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Y.forEach(["delete","get","head","options"],function(e){aK.prototype[e]=function(a,n){return this.request(aw(n||{},{method:e,url:a,data:(n||{}).data}))}}),Y.forEach(["post","put","patch"],function(e){function a(a){return function(n,t,i){return this.request(aw(i||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:n,data:t}))}}aK.prototype[e]=a(),aK.prototype[e+"Form"]=a(!0)});class aX{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let n=this;this.promise.then(e=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](e);n._listeners=null}),this.promise.then=e=>{let a;let t=new Promise(e=>{n.subscribe(e),a=e}).then(e);return t.cancel=function(){n.unsubscribe(a)},t},e(function(e,t,i){n.reason||(n.reason=new eU(e,t,i),a(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aX(function(a){e=a}),cancel:e}}}let aJ={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aJ).forEach(([e,a])=>{aJ[a]=e});let aY=function e(a){let n=new aK(a),t=f(aK.prototype.request,n);return Y.extend(t,aK.prototype,n,{allOwnKeys:!0}),Y.extend(t,n,null,{allOwnKeys:!0}),t.create=function(n){return e(aw(a,n))},t}(eS);aY.Axios=aK,aY.CanceledError=eU,aY.CancelToken=aX,aY.isCancel=eI,aY.VERSION=eV,aY.toFormData=es,aY.AxiosError=Q,aY.Cancel=aY.CanceledError,aY.all=function(e){return Promise.all(e)},aY.spread=function(e){return function(a){return e.apply(null,a)}},aY.isAxiosError=function(e){return Y.isObject(e)&&!0===e.isAxiosError},aY.mergeConfig=aw,aY.AxiosHeaders=eF,aY.formToJSON=e=>eR(Y.isHTMLForm(e)?new FormData(e):e),aY.getAdapter=aM.getAdapter,aY.HttpStatusCode=aJ,aY.default=aY;let aQ=aY},1135:(e,a,n)=>{"use strict";function t(){for(var e,a,n=0,t="",i=arguments.length;n<i;n++)(e=arguments[n])&&(a=function e(a){var n,t,i="";if("string"==typeof a||"number"==typeof a)i+=a;else if("object"==typeof a){if(Array.isArray(a)){var o=a.length;for(n=0;n<o;n++)a[n]&&(t=e(a[n]))&&(i&&(i+=" "),i+=t)}else for(t in a)a[t]&&(i&&(i+=" "),i+=t)}return i}(e))&&(t&&(t+=" "),t+=a);return t}n.d(a,{W:()=>t})},1009:(e,a,n)=>{"use strict";n.d(a,{m6:()=>X});let t=e=>{let a=r(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,a)||s(e)},getConflictingClassGroupIds:(e,a)=>{let i=n[e]||[];return a&&t[e]?[...i,...t[e]]:i}}},i=(e,a)=>{if(0===e.length)return a.classGroupId;let n=e[0],t=a.nextPart.get(n),o=t?i(e.slice(1),t):void 0;if(o)return o;if(0===a.validators.length)return;let s=e.join("-");return a.validators.find(({validator:e})=>e(s))?.classGroupId},o=/^\[(.+)\]$/,s=e=>{if(o.test(e)){let a=o.exec(e)[1],n=a?.substring(0,a.indexOf(":"));if(n)return"arbitrary.."+n}},r=e=>{let{theme:a,prefix:n}=e,t={nextPart:new Map,validators:[]};return u(Object.entries(e.classGroups),n).forEach(([e,n])=>{c(n,t,e,a)}),t},c=(e,a,n,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?a:p(a,e)).classGroupId=n;return}if("function"==typeof e){if(l(e)){c(e(t),a,n,t);return}a.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,i])=>{c(i,p(a,e),n,t)})})},p=(e,a)=>{let n=e;return a.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},l=e=>e.isThemeGetter,u=(e,a)=>a?e.map(([e,n])=>[e,n.map(e=>"string"==typeof e?a+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[a+e,n])):e)]):e,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let a=0,n=new Map,t=new Map,i=(i,o)=>{n.set(i,o),++a>e&&(a=0,t=n,n=new Map)};return{get(e){let a=n.get(e);return void 0!==a?a:void 0!==(a=t.get(e))?(i(e,a),a):void 0},set(e,a){n.has(e)?n.set(e,a):i(e,a)}}},m=e=>{let{separator:a,experimentalParseClassName:n}=e,t=1===a.length,i=a[0],o=a.length,s=e=>{let n;let s=[],r=0,c=0;for(let p=0;p<e.length;p++){let l=e[p];if(0===r){if(l===i&&(t||e.slice(p,p+o)===a)){s.push(e.slice(c,p)),c=p+o;continue}if("/"===l){n=p;continue}}"["===l?r++:"]"===l&&r--}let p=0===s.length?e:e.substring(c),l=p.startsWith("!"),u=l?p.substring(1):p;return{modifiers:s,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:n&&n>c?n-c:void 0}};return n?e=>n({className:e,parseClassName:s}):s},f=e=>{if(e.length<=1)return e;let a=[],n=[];return e.forEach(e=>{"["===e[0]?(a.push(...n.sort(),e),n=[]):n.push(e)}),a.push(...n.sort()),a},x=e=>({cache:d(e.cacheSize),parseClassName:m(e),...t(e)}),h=/\s+/,v=(e,a)=>{let{parseClassName:n,getClassGroupId:t,getConflictingClassGroupIds:i}=a,o=[],s=e.trim().split(h),r="";for(let e=s.length-1;e>=0;e-=1){let a=s[e],{modifiers:c,hasImportantModifier:p,baseClassName:l,maybePostfixModifierPosition:u}=n(a),d=!!u,m=t(d?l.substring(0,u):l);if(!m){if(!d||!(m=t(l))){r=a+(r.length>0?" "+r:r);continue}d=!1}let x=f(c).join(":"),h=p?x+"!":x,v=h+m;if(o.includes(v))continue;o.push(v);let b=i(m,d);for(let e=0;e<b.length;++e){let a=b[e];o.push(h+a)}r=a+(r.length>0?" "+r:r)}return r};function b(){let e,a,n=0,t="";for(;n<arguments.length;)(e=arguments[n++])&&(a=g(e))&&(t&&(t+=" "),t+=a);return t}let g=e=>{let a;if("string"==typeof e)return e;let n="";for(let t=0;t<e.length;t++)e[t]&&(a=g(e[t]))&&(n&&(n+=" "),n+=a);return n},y=e=>{let a=a=>a[e]||[];return a.isThemeGetter=!0,a},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,_=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),k=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,j=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,R=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,A=e=>T(e)||E.has(e)||_.test(e),C=e=>H(e,"length",G),T=e=>!!e&&!Number.isNaN(Number(e)),P=e=>H(e,"number",T),z=e=>!!e&&Number.isInteger(Number(e)),N=e=>e.endsWith("%")&&T(e.slice(0,-1)),F=e=>w.test(e),L=e=>k.test(e),I=new Set(["length","size","percentage"]),U=e=>H(e,I,$),B=e=>H(e,"position",$),D=new Set(["image","url"]),M=e=>H(e,D,K),q=e=>H(e,"",V),W=()=>!0,H=(e,a,n)=>{let t=w.exec(e);return!!t&&(t[1]?"string"==typeof a?t[1]===a:a.has(t[1]):n(t[2]))},G=e=>j.test(e)&&!R.test(e),$=()=>!1,V=e=>S.test(e),K=e=>O.test(e);Symbol.toStringTag;let X=function(e,...a){let n,t,i;let o=function(r){return t=(n=x(a.reduce((e,a)=>a(e),e()))).cache.get,i=n.cache.set,o=s,s(r)};function s(e){let a=t(e);if(a)return a;let o=v(e,n);return i(e,o),o}return function(){return o(b.apply(null,arguments))}}(()=>{let e=y("colors"),a=y("spacing"),n=y("blur"),t=y("brightness"),i=y("borderColor"),o=y("borderRadius"),s=y("borderSpacing"),r=y("borderWidth"),c=y("contrast"),p=y("grayscale"),l=y("hueRotate"),u=y("invert"),d=y("gap"),m=y("gradientColorStops"),f=y("gradientColorStopPositions"),x=y("inset"),h=y("margin"),v=y("opacity"),b=y("padding"),g=y("saturate"),w=y("scale"),_=y("sepia"),E=y("skew"),k=y("space"),j=y("translate"),R=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",F,a],I=()=>[F,a],D=()=>["",A,C],H=()=>["auto",T,F],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",F],J=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Y=()=>[T,F];return{cacheSize:500,separator:":",theme:{colors:[W],spacing:[A,C],blur:["none","",L,F],brightness:Y(),borderColor:[e],borderRadius:["none","","full",L,F],borderSpacing:I(),borderWidth:D(),contrast:Y(),grayscale:X(),hueRotate:Y(),invert:X(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[N,C],inset:O(),margin:O(),opacity:Y(),padding:I(),saturate:Y(),scale:Y(),sepia:X(),skew:Y(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":J()}],"break-before":[{"break-before":J()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),F]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",z,F]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",z,F]}],"grid-cols":[{"grid-cols":[W]}],"col-start-end":[{col:["auto",{span:["full",z,F]},F]}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":[W]}],"row-start-end":[{row:["auto",{span:[z,F]},F]}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...K()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...K(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...K(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,a]}],"min-w":[{"min-w":[F,a,"min","max","fit"]}],"max-w":[{"max-w":[F,a,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[F,a,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,a,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,a,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,a,"auto","min","max","fit"]}],"font-size":[{text:["base",L,C]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",P]}],"font-family":[{font:[W]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",T,P]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",A,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",A,C]}],"underline-offset":[{"underline-offset":["auto",A,F]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),B]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",U]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},M]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[r]}],"border-w-x":[{"border-x":[r]}],"border-w-y":[{"border-y":[r]}],"border-w-s":[{"border-s":[r]}],"border-w-e":[{"border-e":[r]}],"border-w-t":[{"border-t":[r]}],"border-w-r":[{"border-r":[r]}],"border-w-b":[{"border-b":[r]}],"border-w-l":[{"border-l":[r]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[r]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[r]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:$()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[A,F]}],"outline-w":[{outline:[A,C]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[A,C]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,q]}],"shadow-color":[{shadow:[W]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[t]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",L,F]}],grayscale:[{grayscale:[p]}],"hue-rotate":[{"hue-rotate":[l]}],invert:[{invert:[u]}],saturate:[{saturate:[g]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[t]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[p]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[l]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:Y()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:Y()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[z,F]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[A,C,P]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},551:(e,a,n)=>{"use strict";n.d(a,{Ue:()=>d});let t=e=>{let a;let n=new Set,t=(e,t)=>{let i="function"==typeof e?e(a):e;if(!Object.is(i,a)){let e=a;a=(null!=t?t:"object"!=typeof i||null===i)?i:Object.assign({},a,i),n.forEach(n=>n(a,e))}},i=()=>a,o={setState:t,getState:i,getInitialState:()=>s,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},s=a=e(t,i,o);return o},i=e=>e?t(e):t;var o=n(7577),s=n(1508);let{useDebugValue:r}=o,{useSyncExternalStoreWithSelector:c}=s,p=!1,l=e=>e,u=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let a="function"==typeof e?i(e):e,n=(e,n)=>(function(e,a=l,n){n&&!p&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),p=!0);let t=c(e.subscribe,e.getState,e.getServerState||e.getInitialState,a,n);return r(t),t})(a,e,n);return Object.assign(n,a),n},d=e=>e?u(e):u},5251:(e,a,n)=>{"use strict";n.d(a,{tJ:()=>s});let t=e=>a=>{try{let n=e(a);if(n instanceof Promise)return n;return{then:e=>t(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:a=>t(a)(e)}}},i=(e,a)=>(n,i,o)=>{let s,r,c={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,a)=>({...a,...e}),...a},p=!1,l=new Set,u=new Set;try{s=c.getStorage()}catch(e){}if(!s)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${c.name}', the given storage is currently unavailable.`),n(...e)},i,o);let d=t(c.serialize),m=()=>{let e;let a=d({state:c.partialize({...i()}),version:c.version}).then(e=>s.setItem(c.name,e)).catch(a=>{e=a});if(e)throw e;return a},f=o.setState;o.setState=(e,a)=>{f(e,a),m()};let x=e((...e)=>{n(...e),m()},i,o),h=()=>{var e;if(!s)return;p=!1,l.forEach(e=>e(i()));let a=(null==(e=c.onRehydrateStorage)?void 0:e.call(c,i()))||void 0;return t(s.getItem.bind(s))(c.name).then(e=>{if(e)return c.deserialize(e)}).then(e=>{if(e){if("number"!=typeof e.version||e.version===c.version)return e.state;if(c.migrate)return c.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var a;return n(r=c.merge(e,null!=(a=i())?a:x),!0),m()}).then(()=>{null==a||a(r,void 0),p=!0,u.forEach(e=>e(r))}).catch(e=>{null==a||a(void 0,e)})};return o.persist={setOptions:e=>{c={...c,...e},e.getStorage&&(s=e.getStorage())},clearStorage:()=>{null==s||s.removeItem(c.name)},getOptions:()=>c,rehydrate:()=>h(),hasHydrated:()=>p,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},h(),r||x},o=(e,a)=>(n,i,o)=>{let s,r={storage:function(e,a){let n;try{n=e()}catch(e){return}return{getItem:e=>{var a;let t=e=>null===e?null:JSON.parse(e,void 0),i=null!=(a=n.getItem(e))?a:null;return i instanceof Promise?i.then(t):t(i)},setItem:(e,a)=>n.setItem(e,JSON.stringify(a,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,a)=>({...a,...e}),...a},c=!1,p=new Set,l=new Set,u=r.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${r.name}', the given storage is currently unavailable.`),n(...e)},i,o);let d=()=>{let e=r.partialize({...i()});return u.setItem(r.name,{state:e,version:r.version})},m=o.setState;o.setState=(e,a)=>{m(e,a),d()};let f=e((...e)=>{n(...e),d()},i,o);o.getInitialState=()=>f;let x=()=>{var e,a;if(!u)return;c=!1,p.forEach(e=>{var a;return e(null!=(a=i())?a:f)});let o=(null==(a=r.onRehydrateStorage)?void 0:a.call(r,null!=(e=i())?e:f))||void 0;return t(u.getItem.bind(u))(r.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===r.version)return[!1,e.state];if(r.migrate)return[!0,r.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var a;let[t,o]=e;if(n(s=r.merge(o,null!=(a=i())?a:f),!0),t)return d()}).then(()=>{null==o||o(s,void 0),s=i(),c=!0,l.forEach(e=>e(s))}).catch(e=>{null==o||o(void 0,e)})};return o.persist={setOptions:e=>{r={...r,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(r.name)},getOptions:()=>r,rehydrate:()=>x(),hasHydrated:()=>c,onHydrate:e=>(p.add(e),()=>{p.delete(e)}),onFinishHydration:e=>(l.add(e),()=>{l.delete(e)})},r.skipHydration||x(),s||f},s=(e,a)=>"getStorage"in a||"serialize"in a||"deserialize"in a?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),i(e,a)):o(e,a)},572:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};