"""
Task service for database operations and task management
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func
from datetime import datetime

from app.models.task import Task, TaskStatus, TaskPriority
from app.core.exceptions import NotFoundError, ValidationError


class TaskService:
    """Task service for database operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, task_data: Dict[str, Any]) -> Task:
        """Create a new task"""
        task = Task(**task_data)
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def get_by_id(self, task_id: int) -> Optional[Task]:
        """Get task by ID"""
        result = await self.db.execute(
            select(Task).where(Task.id == task_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_user(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None
    ) -> List[Task]:
        """Get tasks by user"""
        query = select(Task).where(Task.user_id == user_id)
        
        if status:
            query = query.where(Task.status == status)
        
        if priority:
            query = query.where(Task.priority == priority)
        
        query = query.offset(skip).limit(limit).order_by(Task.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_agent(
        self,
        agent_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[TaskStatus] = None
    ) -> List[Task]:
        """Get tasks by agent"""
        query = select(Task).where(Task.agent_id == agent_id)
        
        if status:
            query = query.where(Task.status == status)
        
        query = query.offset(skip).limit(limit).order_by(Task.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_pending_tasks(self, limit: int = 100) -> List[Task]:
        """Get pending tasks for execution"""
        query = select(Task).where(
            Task.status == TaskStatus.PENDING
        ).order_by(
            Task.priority.desc(),
            Task.created_at.asc()
        ).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update(self, task_id: int, task_data: Dict[str, Any]) -> Task:
        """Update task"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        # Update fields
        for field, value in task_data.items():
            if hasattr(task, field):
                setattr(task, field, value)
        
        task.updated_at = func.now()
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def delete(self, task_id: int) -> bool:
        """Delete task"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        await self.db.delete(task)
        await self.db.commit()
        return True
    
    async def mark_started(self, task_id: int) -> Task:
        """Mark task as started"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        task.mark_started()
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def mark_completed(self, task_id: int, output_data: Dict[str, Any] = None) -> Task:
        """Mark task as completed"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        task.mark_completed(output_data)
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def mark_failed(self, task_id: int, error_message: str) -> Task:
        """Mark task as failed"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        task.mark_failed(error_message)
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def update_progress(
        self,
        task_id: int,
        percentage: int,
        current_step: str = None
    ) -> Task:
        """Update task progress"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        task.update_progress(percentage, current_step)
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def cancel_task(self, task_id: int) -> Task:
        """Cancel a task"""
        return await self.update(task_id, {"status": TaskStatus.CANCELLED})
    
    async def retry_task(self, task_id: int) -> Task:
        """Retry a failed task"""
        task = await self.get_by_id(task_id)
        if not task:
            raise NotFoundError(f"Task with ID {task_id} not found")
        
        if not task.can_retry:
            raise ValidationError("Task cannot be retried")
        
        task.increment_retry()
        await self.db.commit()
        await self.db.refresh(task)
        return task
    
    async def get_task_stats(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Get task statistics"""
        base_query = select(func.count(Task.id))
        
        if user_id:
            base_query = base_query.where(Task.user_id == user_id)
        
        # Get counts by status
        stats = {}
        for status in TaskStatus:
            query = base_query.where(Task.status == status)
            count = await self.db.scalar(query)
            stats[f"{status.value}_count"] = count or 0
        
        # Get total count
        total_count = await self.db.scalar(base_query)
        stats["total_count"] = total_count or 0
        
        return stats
    
    async def search_tasks(
        self,
        search_term: str,
        user_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Task]:
        """Search tasks by title or description"""
        search_pattern = f"%{search_term}%"
        
        query = select(Task).where(
            (Task.title.ilike(search_pattern)) |
            (Task.description.ilike(search_pattern))
        )
        
        if user_id:
            query = query.where(Task.user_id == user_id)
        
        query = query.offset(skip).limit(limit).order_by(Task.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
