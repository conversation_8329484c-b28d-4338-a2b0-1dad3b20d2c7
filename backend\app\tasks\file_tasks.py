"""
File processing tasks
"""

from celery import current_task
from app.core.celery import celery_app
from app.core.database import get_async_session_local
from app.services.file_service import FileService
from app.core.socketio_manager import send_to_user
from loguru import logger
import asyncio
import hashlib
import mimetypes
from pathlib import Path


@celery_app.task(bind=True)
def process_uploaded_file(self, file_id: int, user_id: int):
    """Process an uploaded file"""
    
    async def _process():
        AsyncSessionLocal = get_async_session_local()
        async with AsyncSessionLocal() as db:
            file_service = FileService(db)
            
            try:
                # Get file
                file_obj = await file_service.get_by_id(file_id)
                if not file_obj:
                    raise Exception("File not found")
                
                # Update status to processing
                await file_service.update_status(file_id, "processing")
                
                # Send real-time update
                await send_to_user(user_id, "file_processing_started", {
                    "file_id": file_id,
                    "filename": file_obj.filename,
                    "status": "processing"
                })
                
                # Update progress
                current_task.update_state(
                    state='PROGRESS',
                    meta={'current': 10, 'total': 100, 'status': 'Analyzing file...'}
                )
                
                # Analyze file content
                content_analysis = await analyze_file_content(file_obj)
                
                current_task.update_state(
                    state='PROGRESS',
                    meta={'current': 50, 'total': 100, 'status': 'Extracting metadata...'}
                )
                
                # Extract metadata
                metadata = await extract_file_metadata(file_obj)
                
                current_task.update_state(
                    state='PROGRESS',
                    meta={'current': 80, 'total': 100, 'status': 'Finalizing...'}
                )
                
                # Update file with analysis results
                await file_service.update(file_id, {
                    "status": "ready",
                    "processing_status": "completed",
                    "processing_progress": 100,
                    "extracted_text": content_analysis.get("text"),
                    "metadata": {**file_obj.metadata, **metadata, **content_analysis}
                })
                
                current_task.update_state(
                    state='SUCCESS',
                    meta={'current': 100, 'total': 100, 'status': 'Processing completed'}
                )
                
                # Send completion notification
                await send_to_user(user_id, "file_processing_completed", {
                    "file_id": file_id,
                    "filename": file_obj.filename,
                    "status": "ready",
                    "metadata": metadata
                })
                
                return {
                    "success": True,
                    "file_id": file_id,
                    "analysis": content_analysis,
                    "metadata": metadata
                }
                
            except Exception as e:
                logger.error(f"File processing failed: {e}")
                
                # Mark file as error
                await file_service.update(file_id, {
                    "status": "error",
                    "processing_status": "failed",
                    "processing_error": str(e)
                })
                
                # Send failure notification
                await send_to_user(user_id, "file_processing_failed", {
                    "file_id": file_id,
                    "error": str(e)
                })
                
                raise
    
    # Run the async function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_process())
    finally:
        loop.close()


async def analyze_file_content(file_obj) -> dict:
    """Analyze file content and extract text"""
    analysis = {
        "text": None,
        "word_count": 0,
        "language": None,
        "content_type": None
    }
    
    try:
        # Determine content type
        mime_type = file_obj.mime_type
        
        if mime_type.startswith('text/'):
            # Text files
            # In a real implementation, you would read the file content
            # For now, simulate text extraction
            analysis["text"] = f"Sample text content from {file_obj.filename}"
            analysis["word_count"] = len(analysis["text"].split())
            analysis["content_type"] = "text"
            
        elif mime_type.startswith('image/'):
            # Image files
            analysis["content_type"] = "image"
            # Could use OCR here
            
        elif mime_type == 'application/pdf':
            # PDF files
            analysis["content_type"] = "pdf"
            # Could use PDF text extraction here
            
        elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            # Word documents
            analysis["content_type"] = "document"
            # Could use document parsing here
            
        else:
            analysis["content_type"] = "other"
    
    except Exception as e:
        logger.error(f"Content analysis failed: {e}")
    
    return analysis


async def extract_file_metadata(file_obj) -> dict:
    """Extract file metadata"""
    metadata = {
        "file_extension": file_obj.file_extension,
        "mime_type": file_obj.mime_type,
        "size_mb": file_obj.file_size_mb,
        "processed_at": file_obj.updated_at.isoformat() if file_obj.updated_at else None
    }
    
    # Add file-type specific metadata
    if file_obj.mime_type.startswith('image/'):
        # Image metadata (dimensions, etc.)
        metadata.update({
            "type": "image",
            "format": file_obj.file_extension.upper()
        })
    
    elif file_obj.mime_type.startswith('text/'):
        # Text metadata
        metadata.update({
            "type": "text",
            "encoding": "utf-8"  # Default assumption
        })
    
    return metadata


@celery_app.task
def cleanup_temporary_files():
    """Clean up temporary files"""

    async def _cleanup():
        AsyncSessionLocal = get_async_session_local()
        async with AsyncSessionLocal() as db:
            file_service = FileService(db)

            try:
                # Get temporary files older than 24 hours
                expired_files = await file_service.get_expired_temporary_files()

                for file_obj in expired_files:
                    try:
                        # Delete from storage
                        await file_service.delete_from_storage(file_obj.id)

                        # Delete from database
                        await file_service.delete(file_obj.id)

                        logger.info(f"Cleaned up temporary file: {file_obj.filename}")

                    except Exception as e:
                        logger.error(f"Failed to cleanup file {file_obj.id}: {e}")

                # Also cleanup local storage temp files
                from app.services.local_storage_service import local_storage
                local_cleanup_count = local_storage.cleanup_temp_files()

                return len(expired_files) + local_cleanup_count

            except Exception as e:
                logger.error(f"Cleanup task failed: {e}")
                raise

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_cleanup())
    finally:
        loop.close()


@celery_app.task(bind=True)
def generate_file_thumbnail(self, file_id: int):
    """Generate thumbnail for image files"""
    
    async def _generate():
        AsyncSessionLocal = get_async_session_local()
        async with AsyncSessionLocal() as db:
            file_service = FileService(db)
            
            try:
                file_obj = await file_service.get_by_id(file_id)
                if not file_obj:
                    raise Exception("File not found")
                
                if not file_obj.mime_type.startswith('image/'):
                    return {"message": "Not an image file"}
                
                # In a real implementation, you would:
                # 1. Download the image from storage
                # 2. Generate thumbnail using PIL or similar
                # 3. Upload thumbnail back to storage
                # 4. Update file metadata with thumbnail URL
                
                # For now, simulate thumbnail generation
                thumbnail_url = f"/thumbnails/{file_obj.id}_thumb.jpg"
                
                # Update file metadata
                metadata = file_obj.metadata or {}
                metadata["thumbnail_url"] = thumbnail_url
                
                await file_service.update(file_id, {"metadata": metadata})
                
                return {
                    "success": True,
                    "thumbnail_url": thumbnail_url
                }
                
            except Exception as e:
                logger.error(f"Thumbnail generation failed: {e}")
                raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_generate())
    finally:
        loop.close()
