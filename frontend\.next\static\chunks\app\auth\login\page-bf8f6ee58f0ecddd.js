(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[716],{6298:function(e,t,r){Promise.resolve().then(r.bind(r,1202))},1202:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return x}});var s=r(7437),a=r(2265),n=r(9376),o=r(7648),i=r(9501),l=r(3590),c=r(1115),u=r(9064),d=r(2087),h=r(6502),m=r(2548),f=r(1770),p=r(7692);let g=c.z.object({email:c.z.string().email("Please enter a valid email address"),password:c.z.string().min(1,"Password is required")});function x(){let[e,t]=(0,a.useState)(!1),r=(0,n.useRouter)(),{login:c,user:x,isLoading:y}=(0,f.t)(),{register:w,handleSubmit:b,formState:{errors:v,isSubmitting:j}}=(0,i.cI)({resolver:(0,l.F)(g)});(0,a.useEffect)(()=>{x&&!y&&r.push("/dashboard")},[x,y,r]);let k=async e=>{try{await c(e),u.Am.success("Welcome back!"),r.push("/dashboard")}catch(e){var t,s;u.Am.error((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.detail)||"Login failed")}};return y?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(p.T,{size:"lg"})}):x?null:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)(o.default,{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)(d.Z,{className:"h-12 w-12 text-blue-600"}),(0,s.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"Agentico"})]})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(o.default,{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:b(k),children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsx)("input",{...w("email"),type:"email",autoComplete:"email",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your email"}),v.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.email.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"mt-1 relative",children:[(0,s.jsx)("input",{...w("password"),type:e?"text":"password",autoComplete:"current-password",className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?(0,s.jsx)(h.Z,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(m.Z,{className:"h-5 w-5 text-gray-400"})})]}),v.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.password.message})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(o.default,{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:j,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,s.jsx)(p.T,{size:"sm"}):"Sign in"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(o.default,{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up here"})]})})]})]})})}},7692:function(e,t,r){"use strict";r.d(t,{T:function(){return o}});var s=r(7437),a=r(3448);let n={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"};function o(e){let{size:t="md",className:r}=e;return(0,s.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",n[t],r)})}},3227:function(e,t,r){"use strict";r.d(t,{x:function(){return i}});var s=r(3464),a=r(9064);let n=r(257).env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class o{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=localStorage.getItem("auth-storage");if(t)try{let{state:r}=JSON.parse(t);(null==r?void 0:r.token)&&(e.headers.Authorization="Bearer ".concat(r.token))}catch(e){console.error("Error parsing auth storage:",e)}return e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){r._retry=!0;try{let e=localStorage.getItem("auth-storage");if(e){let{state:t}=JSON.parse(e);if(null==t?void 0:t.refreshToken){let{access_token:e,refresh_token:s}=(await this.client.post("/auth/refresh",{refresh_token:t.refreshToken})).data,a={...t,token:e,refreshToken:s};return localStorage.setItem("auth-storage",JSON.stringify({state:a,version:0})),r.headers.Authorization="Bearer ".concat(e),this.client(r)}}}catch(e){return localStorage.removeItem("auth-storage"),window.location.href="/auth/login",Promise.reject(e)}}return this.handleError(e),Promise.reject(e)})}handleError(e){if(e.response){let{status:t,data:r}=e.response;switch(t){case 400:a.Am.error(r.detail||"Bad request");break;case 401:a.Am.error("Authentication required");break;case 403:a.Am.error("Access denied");break;case 404:a.Am.error("Resource not found");break;case 422:a.Am.error(r.detail||"Validation error");break;case 429:a.Am.error("Too many requests. Please try again later.");break;case 500:a.Am.error("Server error. Please try again later.");break;default:a.Am.error(r.detail||"An error occurred")}}else e.request?a.Am.error("Network error. Please check your connection."):a.Am.error("An unexpected error occurred")}async get(e,t){return this.client.get(e,t)}async post(e,t,r){return this.client.post(e,t,r)}async put(e,t,r){return this.client.put(e,t,r)}async patch(e,t,r){return this.client.patch(e,t,r)}async delete(e,t){return this.client.delete(e,t)}async uploadFile(e,t,r){let s=new FormData;return s.append("file",t),this.client.post(e,s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{r&&e.total&&r(Math.round(100*e.loaded/e.total))}})}constructor(){this.client=s.Z.create({baseURL:"".concat(n,"/api/v1"),timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}}let i=new o},3448:function(e,t,r){"use strict";r.d(t,{SY:function(){return o},cn:function(){return n}});var s=r(1994),a=r(3335);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}function o(e){let t=new Date,r=new Date(e),s=Math.floor((t.getTime()-r.getTime())/1e3);if(s<60)return"just now";let a=Math.floor(s/60);if(a<60)return"".concat(a,"m ago");let n=Math.floor(a/60);if(n<24)return"".concat(n,"h ago");let o=Math.floor(n/24);return o<7?"".concat(o,"d ago"):new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e))}},1770:function(e,t,r){"use strict";r.d(t,{t:function(){return l}});var s=r(9625),a=r(6885),n=r(3227);class o{async login(e){return(await n.x.post("/auth/login",e)).data}async register(e){return(await n.x.post("/auth/register",e)).data}async logout(){await n.x.post("/auth/logout")}async getCurrentUser(){return(await n.x.get("/auth/me")).data}async refreshToken(e){return(await n.x.post("/auth/refresh",{refresh_token:e})).data}async requestPasswordReset(e){return(await n.x.post("/auth/password-reset",{email:e})).data}async confirmPasswordReset(e,t){return(await n.x.post("/auth/password-reset/confirm",{token:e,new_password:t})).data}}let i=new o,l=(0,s.Ue)()((0,a.tJ)((e,t)=>({user:null,token:null,refreshToken:null,isLoading:!1,isAuthenticated:!1,login:async t=>{e({isLoading:!0});try{let r=await i.login(t);e({user:r.user,token:r.access_token,refreshToken:r.refresh_token,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{await i.register(t),e({isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:()=>{e({user:null,token:null,refreshToken:null,isAuthenticated:!1}),localStorage.removeItem("auth-storage")},checkAuth:async()=>{let{token:r}=t();if(!r){e({isLoading:!1});return}e({isLoading:!0});try{let t=await i.getCurrentUser();e({user:t,isAuthenticated:!0,isLoading:!1})}catch(r){try{await t().refreshAuth()}catch(e){t().logout()}e({isLoading:!1})}},refreshAuth:async()=>{let{refreshToken:r}=t();if(!r)throw Error("No refresh token available");try{let t=await i.refreshToken(r);e({token:t.access_token,refreshToken:t.refresh_token})}catch(e){throw t().logout(),e}},updateUser:r=>{let{user:s}=t();s&&e({user:{...s,...r}})}}),{name:"auth-storage",partialize:e=>({token:e.token,refreshToken:e.refreshToken,user:e.user})}))}},function(e){e.O(0,[64,565,45,971,117,744],function(){return e(e.s=6298)}),_N_E=e.O()}]);