(()=>{var e={};e.id=409,e.ids=[409],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>c});var n=r(1355),o=r(862),s=r(5745),a=r.n(s),i=r(4635),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let c=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,6608,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2808)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,6608,23)),"next/dist/client/components/not-found-error"]}],l=[],u="/_not-found/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8828:(e,t,r)=>{Promise.resolve().then(r.bind(r,5503))},272:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3907,23)),Promise.resolve().then(r.t.bind(r,7648,23)),Promise.resolve().then(r.t.bind(r,1944,23)),Promise.resolve().then(r.t.bind(r,9297,23)),Promise.resolve().then(r.t.bind(r,2649,23)),Promise.resolve().then(r.t.bind(r,4423,23))},5503:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>a});var n=r(5452);(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}();var o=r(8268),s=r(2339);function a({children:e}){let[t]=(0,s.useState)(()=>Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}())({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@tanstack/react-query'");throw e.code="MODULE_NOT_FOUND",e}()),{client:t,children:[n.jsx(o.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),n.jsx(Object(function(){var e=Error("Cannot find module '@tanstack/react-query-devtools'");throw e.code="MODULE_NOT_FOUND",e}()),{initialIsOpen:!1})]})}},2808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var n=r(1949),o=r(8898),s=r.n(o);r(98);var a=r(7873);let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx`),{__esModule:d,$$typeof:c}=i;i.default;let l=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);!function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}();let u={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function p({children:e}){return n.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:n.jsx("body",{className:s().className,children:(0,n.jsxs)(l,{children:[e,n.jsx(Object(function(){var e=Error("Cannot find module 'react-hot-toast'");throw e.code="MODULE_NOT_FOUND",e}()),{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},98:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[341],()=>r(3625));module.exports=n})();