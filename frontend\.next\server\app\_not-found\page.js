(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3277:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>l});var n=r(3191),s=r(8716),o=r(7922),i=r.n(o),a=r(5231),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4968)),"C:\\Users\\<USER>\\Desktop\\agentico\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},510:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6563:(e,t,r)=>{Promise.resolve().then(r.bind(r,381)),Promise.resolve().then(r.bind(r,1012))},1012:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>l});var n=r(326),s=r(3244),o=r(4976),i=r(787),a=r(3574),d=r(7577);function l({children:e}){let[t]=(0,d.useState)(()=>new s.S({defaultOptions:{queries:{staleTime:6e4,retry:(e,t)=>!(t?.response?.status>=400&&t?.response?.status<500)&&e<3},mutations:{retry:!1}}}));return(0,n.jsxs)(o.aH,{client:t,children:[n.jsx(a.f,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),n.jsx(i.t,{initialIsOpen:!1})]})}},4968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>d});var n=r(9510),s=r(5384),o=r.n(s);r(5023);let i=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\agentico\frontend\src\components\providers.tsx#Providers`);var a=r(9125);let d={title:"Agentico - AI Agent Platform",description:"Next-generation AI agent platform for enhanced productivity and automation",keywords:["AI","agents","automation","productivity","platform"],authors:[{name:"Agentico Team"}],viewport:"width=device-width, initial-scale=1",themeColor:"#000000"};function l({children:e}){return n.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:n.jsx("body",{className:o().className,children:(0,n.jsxs)(i,{children:[e,n.jsx(a.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[497],()=>r(3277));module.exports=n})();