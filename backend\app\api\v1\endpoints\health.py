"""
Health check endpoints
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import redis
from datetime import datetime

from app.core.database import get_async_db, check_database_health
from app.core.config import settings

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "0.1.0",
        "environment": settings.ENVIRONMENT
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_async_db)):
    """Detailed health check including dependencies"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "0.1.0",
        "environment": settings.ENVIRONMENT,
        "services": {}
    }
    
    # Check database
    try:
        db_healthy = await check_database_health()
        health_status["services"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "type": "postgresql"
        }
    except Exception as e:
        health_status["services"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "postgresql"
        }
        health_status["status"] = "degraded"
    
    # Check Redis
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        health_status["services"]["redis"] = {
            "status": "healthy",
            "type": "redis"
        }
    except Exception as e:
        health_status["services"]["redis"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "redis"
        }
        health_status["status"] = "degraded"
    
    return health_status


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_async_db)):
    """Readiness check for Kubernetes"""
    try:
        # Check if database is ready
        db_healthy = await check_database_health()
        if not db_healthy:
            return {"status": "not ready", "reason": "database not ready"}, 503
        
        # Check if Redis is ready
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        
        return {"status": "ready"}
    
    except Exception as e:
        return {"status": "not ready", "reason": str(e)}, 503


@router.get("/live")
async def liveness_check():
    """Liveness check for Kubernetes"""
    return {"status": "alive"}
