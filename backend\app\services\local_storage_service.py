"""
Local file storage service to replace MinIO
Provides S3-compatible interface for local file operations
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, BinaryIO, Dict, Any
from datetime import datetime
import uuid
from loguru import logger

from app.core.config import settings


class LocalStorageService:
    """Local file storage service with S3-compatible interface"""
    
    def __init__(self):
        self.storage_path = Path(settings.FILE_STORAGE_PATH)
        self.upload_path = Path(settings.FILE_UPLOAD_PATH)
        
        # Create directories if they don't exist
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.upload_path.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for organization
        (self.storage_path / "files").mkdir(exist_ok=True)
        (self.storage_path / "thumbnails").mkdir(exist_ok=True)
        (self.storage_path / "temp").mkdir(exist_ok=True)
        (self.upload_path / "temp").mkdir(exist_ok=True)
    
    def generate_file_path(self, filename: str, user_id: int) -> str:
        """Generate a unique file path"""
        # Create user-specific directory
        user_dir = self.storage_path / "files" / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename to avoid conflicts
        file_ext = Path(filename).suffix
        unique_name = f"{uuid.uuid4().hex}{file_ext}"
        
        return str(user_dir / unique_name)
    
    def upload_file(
        self,
        file_data: BinaryIO,
        filename: str,
        user_id: int,
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload file to local storage"""
        try:
            # Generate file path
            file_path = self.generate_file_path(filename, user_id)
            
            # Calculate file hash while writing
            hasher = hashlib.sha256()
            file_size = 0
            
            with open(file_path, 'wb') as f:
                while chunk := file_data.read(8192):
                    hasher.update(chunk)
                    f.write(chunk)
                    file_size += len(chunk)
            
            # Get file info
            file_hash = hasher.hexdigest()
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
            
            # Generate public URL
            relative_path = Path(file_path).relative_to(self.storage_path)
            public_url = f"/api/v1/files/download/{relative_path}"
            
            logger.info(f"Uploaded file: {filename} -> {file_path}")
            
            return {
                "file_path": file_path,
                "public_url": public_url,
                "file_size": file_size,
                "file_hash": file_hash,
                "content_type": content_type,
                "storage_type": "local"
            }
            
        except Exception as e:
            logger.error(f"Failed to upload file {filename}: {e}")
            raise
    
    def download_file(self, file_path: str) -> Optional[BinaryIO]:
        """Download file from local storage"""
        try:
            full_path = self.storage_path / file_path
            if full_path.exists() and full_path.is_file():
                return open(full_path, 'rb')
            return None
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            return None
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from local storage"""
        try:
            full_path = Path(file_path)
            if full_path.exists() and full_path.is_file():
                full_path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists"""
        try:
            full_path = Path(file_path)
            return full_path.exists() and full_path.is_file()
        except Exception:
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get file information"""
        try:
            full_path = Path(file_path)
            if not full_path.exists():
                return None
            
            stat = full_path.stat()
            content_type, _ = mimetypes.guess_type(str(full_path))
            
            return {
                "file_path": str(full_path),
                "file_size": stat.st_size,
                "content_type": content_type,
                "created_at": datetime.fromtimestamp(stat.st_ctime),
                "modified_at": datetime.fromtimestamp(stat.st_mtime),
                "storage_type": "local"
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return None
    
    def copy_file(self, source_path: str, dest_path: str) -> bool:
        """Copy file within storage"""
        try:
            source = Path(source_path)
            dest = Path(dest_path)
            
            # Create destination directory if needed
            dest.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source, dest)
            logger.info(f"Copied file: {source_path} -> {dest_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to copy file {source_path} -> {dest_path}: {e}")
            return False
    
    def move_file(self, source_path: str, dest_path: str) -> bool:
        """Move file within storage"""
        try:
            source = Path(source_path)
            dest = Path(dest_path)
            
            # Create destination directory if needed
            dest.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(source, dest)
            logger.info(f"Moved file: {source_path} -> {dest_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to move file {source_path} -> {dest_path}: {e}")
            return False
    
    def list_files(self, prefix: str = "", user_id: Optional[int] = None) -> list:
        """List files in storage"""
        try:
            if user_id:
                search_path = self.storage_path / "files" / str(user_id)
            else:
                search_path = self.storage_path / "files"
            
            if not search_path.exists():
                return []
            
            files = []
            for file_path in search_path.rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(self.storage_path)
                    if str(relative_path).startswith(prefix):
                        files.append({
                            "path": str(relative_path),
                            "size": file_path.stat().st_size,
                            "modified": datetime.fromtimestamp(file_path.stat().st_mtime)
                        })
            
            return files
        except Exception as e:
            logger.error(f"Failed to list files: {e}")
            return []
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """Clean up temporary files older than max_age_hours"""
        try:
            temp_dirs = [
                self.storage_path / "temp",
                self.upload_path / "temp"
            ]
            
            cleaned_count = 0
            cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
            
            for temp_dir in temp_dirs:
                if not temp_dir.exists():
                    continue
                
                for file_path in temp_dir.rglob("*"):
                    if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception as e:
                            logger.warning(f"Failed to delete temp file {file_path}: {e}")
            
            logger.info(f"Cleaned up {cleaned_count} temporary files")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            total_size = 0
            file_count = 0
            
            for file_path in self.storage_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            return {
                "total_files": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "storage_path": str(self.storage_path),
                "storage_type": "local"
            }
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {
                "total_files": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "storage_path": str(self.storage_path),
                "storage_type": "local",
                "error": str(e)
            }


# Global instance
local_storage = LocalStorageService()
