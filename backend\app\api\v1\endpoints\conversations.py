"""
Conversation endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_async_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.conversation import Conversation, Message, ConversationStatus, MessageRole
from app.services.conversation_service import ConversationService
from app.core.exceptions import NotFoundError

router = APIRouter()


class ConversationCreate(BaseModel):
    title: str
    description: Optional[str] = None
    agent_id: Optional[int] = None
    context: Optional[dict] = {}


class ConversationUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ConversationStatus] = None
    context: Optional[dict] = None


class MessageCreate(BaseModel):
    content: str
    role: MessageRole = MessageRole.USER
    metadata: Optional[dict] = {}


@router.get("/", response_model=List[dict])
async def get_conversations(
    skip: int = 0,
    limit: int = 100,
    status: Optional[ConversationStatus] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's conversations"""
    conversation_service = ConversationService(db)
    conversations = await conversation_service.get_by_user(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status
    )
    return [conv.to_dict() for conv in conversations]


@router.post("/", response_model=dict)
async def create_conversation(
    conversation_data: ConversationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new conversation"""
    conversation_service = ConversationService(db)
    
    create_data = conversation_data.dict()
    create_data["user_id"] = current_user.id
    create_data["status"] = ConversationStatus.ACTIVE
    
    try:
        conversation = await conversation_service.create(create_data)
        return conversation.to_dict()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create conversation: {str(e)}"
        )


@router.get("/{conversation_id}", response_model=dict)
async def get_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get conversation by ID"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return conversation.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )


@router.put("/{conversation_id}", response_model=dict)
async def update_conversation(
    conversation_id: int,
    conversation_data: ConversationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Update conversation"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update conversation
        update_data = {k: v for k, v in conversation_data.dict().items() if v is not None}
        updated_conversation = await conversation_service.update(conversation_id, update_data)
        
        return updated_conversation.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )


@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete conversation"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        await conversation_service.delete(conversation_id)
        return {"message": "Conversation deleted successfully"}
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )


@router.get("/{conversation_id}/messages", response_model=List[dict])
async def get_messages(
    conversation_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get conversation messages"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        messages = await conversation_service.get_messages(
            conversation_id=conversation_id,
            skip=skip,
            limit=limit
        )
        
        return [msg.to_dict() for msg in messages]
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )


@router.post("/{conversation_id}/messages", response_model=dict)
async def create_message(
    conversation_id: int,
    message_data: MessageCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new message in conversation"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Create message
        message = await conversation_service.add_message(
            conversation_id=conversation_id,
            role=message_data.role,
            content=message_data.content,
            metadata=message_data.metadata
        )
        
        # If this is a user message and there's an agent, trigger agent response
        if message_data.role == MessageRole.USER and conversation.agent_id:
            from app.core.socketio_manager import send_to_user
            from app.tasks.agent_tasks import execute_agent_conversation_task
            
            # Send real-time notification
            await send_to_user(current_user.id, "message_created", {
                "conversation_id": conversation_id,
                "message": message.to_dict()
            })
            
            # Trigger agent response
            execute_agent_conversation_task.delay(
                conversation_id=conversation_id,
                agent_id=conversation.agent_id,
                user_id=current_user.id
            )
        
        return message.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )


@router.post("/{conversation_id}/archive")
async def archive_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Archive conversation"""
    conversation_service = ConversationService(db)
    
    try:
        conversation = await conversation_service.get_by_id(conversation_id)
        if not conversation:
            raise NotFoundError(f"Conversation with ID {conversation_id} not found")
        
        # Check permissions
        if conversation.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Archive conversation
        updated_conversation = await conversation_service.update(conversation_id, {
            "status": ConversationStatus.ARCHIVED,
            "is_archived": True
        })
        
        return {"message": "Conversation archived successfully"}
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
