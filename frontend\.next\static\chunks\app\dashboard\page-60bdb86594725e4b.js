(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{2284:function(e,t,s){Promise.resolve().then(s.bind(s,6625))},6625:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return D}});var a=s(7437),n=s(2265),r=s(9376),i=s(1770),c=s(5836),l=s(1713),o=s(1331),d=s(3767),m=s(7626);let h=n.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?n.createElement("title",{id:a},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var g=s(1244),u=s(3227);class x{async getConversations(e){return(await u.x.get("/conversations",{params:e})).data}async getConversation(e){return(await u.x.get("/conversations/".concat(e))).data}async createConversation(e){return(await u.x.post("/conversations",e)).data}async updateConversation(e,t){return(await u.x.put("/conversations/".concat(e),t)).data}async deleteConversation(e){await u.x.delete("/conversations/".concat(e))}async getMessages(e,t){return(await u.x.get("/conversations/".concat(e,"/messages"),{params:t})).data}async createMessage(e,t){return(await u.x.post("/conversations/".concat(e,"/messages"),t)).data}async archiveConversation(e){await u.x.post("/conversations/".concat(e,"/archive"))}async searchConversations(e,t){return(await u.x.get("/conversations/search",{params:{q:e,...t}})).data}}let v=new x;var p=s(7692),f=s(3448);let w={blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"},j={positive:"text-green-600",negative:"text-red-600",neutral:"text-gray-600"};function y(e){let{name:t,value:s,icon:n,color:r,change:i,changeType:c="neutral"}=e;return(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:(0,f.cn)("p-3 rounded-md",w[r]),children:(0,a.jsx)(n,{className:"h-6 w-6 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:t}),(0,a.jsxs)("dd",{className:"flex items-baseline",children:[(0,a.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:s}),i&&(0,a.jsx)("div",{className:(0,f.cn)("ml-2 text-sm font-medium",j[c]),children:i})]})]})})]})})})}let b=n.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?n.createElement("title",{id:a},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),N=n.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?n.createElement("title",{id:a},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),k=[{id:1,type:"agent_created",title:"Created new Code Agent",description:"Python development assistant",timestamp:new Date(Date.now()-18e5).toISOString(),icon:o.Z,iconColor:"text-blue-500",status:"success"},{id:2,type:"conversation_started",title:"Started conversation with Research Agent",description:"Market analysis discussion",timestamp:new Date(Date.now()-72e5).toISOString(),icon:d.Z,iconColor:"text-green-500",status:"success"},{id:3,type:"task_completed",title:"Task completed successfully",description:"Data analysis report generated",timestamp:new Date(Date.now()-144e5).toISOString(),icon:b,iconColor:"text-green-500",status:"success"},{id:4,type:"task_failed",title:"Task execution failed",description:"API connection timeout",timestamp:new Date(Date.now()-216e5).toISOString(),icon:N,iconColor:"text-red-500",status:"error"}];function C(){let{data:e=k,isLoading:t}=(0,l.a)({queryKey:["recent-activity"],queryFn:async()=>(await new Promise(e=>setTimeout(e,500)),k)});return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),t?(0,a.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,a.jsx)(p.T,{})}):(0,a.jsx)("div",{className:"flow-root",children:(0,a.jsx)("ul",{className:"-mb-8",children:e.map((t,s)=>(0,a.jsx)("li",{children:(0,a.jsxs)("div",{className:"relative pb-8",children:[s!==e.length-1?(0,a.jsx)("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,a.jsxs)("div",{className:"relative flex space-x-3",children:[(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:"h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ".concat("success"===t.status?"bg-green-500":"bg-red-500"),children:(0,a.jsx)(t.icon,{className:"h-5 w-5 text-white"})})}),(0,a.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 font-medium",children:t.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t.description})]}),(0,a.jsx)("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:(0,f.SY)(t.timestamp)})]})]})]})},t.id))})}),!t&&0===e.length&&(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No recent activity"})})]})}var E=s(7648);let A=n.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?n.createElement("title",{id:a},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12-3-3m0 0-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});var Z=s(3836);let S=[{name:"Create Agent",description:"Build a new AI agent for your tasks",href:"/dashboard/agents/new",icon:o.Z,color:"bg-blue-500 hover:bg-blue-600"},{name:"Start Conversation",description:"Chat with your AI agents",href:"/dashboard/conversations/new",icon:d.Z,color:"bg-green-500 hover:bg-green-600"},{name:"Upload Files",description:"Add files for agent processing",href:"/dashboard/files/upload",icon:A,color:"bg-purple-500 hover:bg-purple-600"}];function T(){return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Quick Actions"}),(0,a.jsx)(Z.Z,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:S.map(e=>(0,a.jsxs)(E.default,{href:e.href,className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors",children:[(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:"rounded-lg inline-flex p-3 text-white ".concat(e.color," transition-colors"),children:(0,a.jsx)(e.icon,{className:"h-6 w-6"})})}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:[(0,a.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),e.name]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:e.description})]})]},e.name))})]})}function _(){let{data:e,isLoading:t}=(0,l.a)({queryKey:["agents"],queryFn:()=>g.G.getAgents()}),{data:s,isLoading:n}=(0,l.a)({queryKey:["conversations"],queryFn:()=>v.getConversations()});if(t||n)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(p.T,{size:"lg"})});let r=[{name:"Active Agents",value:(null==e?void 0:e.filter(e=>e.is_active).length)||0,icon:o.Z,color:"blue",change:"+12%",changeType:"positive"},{name:"Conversations",value:(null==s?void 0:s.length)||0,icon:d.Z,color:"green",change:"+5%",changeType:"positive"},{name:"Files Processed",value:24,icon:m.Z,color:"purple",change:"+18%",changeType:"positive"},{name:"Avg Response Time",value:"2.3s",icon:h,color:"orange",change:"-8%",changeType:"positive"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Welcome back! Here's what's happening with your AI agents."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:r.map(e=>(0,a.jsx)(y,{...e},e.name))}),(0,a.jsx)(T,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)(C,{}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Agent Performance"}),(0,a.jsx)("div",{className:"h-64 flex items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.Z,{className:"h-12 w-12 mx-auto mb-2 text-gray-400"}),(0,a.jsx)("p",{children:"Performance charts coming soon"})]})})]})]})]})}function D(){let e=(0,r.useRouter)(),{user:t,isLoading:s,checkAuth:l}=(0,i.t)();return((0,n.useEffect)(()=>{l()},[l]),(0,n.useEffect)(()=>{s||t||e.push("/auth/login")},[t,s,e]),s)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(p.T,{size:"lg"})}):t?(0,a.jsx)(c.c,{children:(0,a.jsx)(_,{})}):null}}},function(e){e.O(0,[64,565,763,140,291,971,117,744],function(){return e(e.s=2284)}),_N_E=e.O()}]);