"""
Agent service for database operations and agent management
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func
from datetime import datetime

from app.models.agent import Agent, AgentStatus, AgentType
from app.core.exceptions import NotFoundError, ValidationError


class AgentService:
    """Agent service for database operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, agent_data: Dict[str, Any]) -> Agent:
        """Create a new agent"""
        agent = Agent(**agent_data)
        self.db.add(agent)
        await self.db.commit()
        await self.db.refresh(agent)
        return agent
    
    async def get_by_id(self, agent_id: int) -> Optional[Agent]:
        """Get agent by ID"""
        result = await self.db.execute(
            select(Agent).where(Agent.id == agent_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_owner(
        self,
        owner_id: int,
        skip: int = 0,
        limit: int = 100,
        agent_type: Optional[AgentType] = None,
        is_active: Optional[bool] = None
    ) -> List[Agent]:
        """Get agents by owner"""
        query = select(Agent).where(Agent.owner_id == owner_id)
        
        if agent_type:
            query = query.where(Agent.agent_type == agent_type)
        
        if is_active is not None:
            query = query.where(Agent.is_active == is_active)
        
        query = query.offset(skip).limit(limit).order_by(Agent.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_public_agents(
        self,
        skip: int = 0,
        limit: int = 100,
        agent_type: Optional[AgentType] = None
    ) -> List[Agent]:
        """Get public agents"""
        query = select(Agent).where(Agent.is_public == True, Agent.is_active == True)
        
        if agent_type:
            query = query.where(Agent.agent_type == agent_type)
        
        query = query.offset(skip).limit(limit).order_by(Agent.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update(self, agent_id: int, agent_data: Dict[str, Any]) -> Agent:
        """Update agent"""
        agent = await self.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Update fields
        for field, value in agent_data.items():
            if hasattr(agent, field):
                setattr(agent, field, value)
        
        agent.updated_at = func.now()
        await self.db.commit()
        await self.db.refresh(agent)
        return agent
    
    async def delete(self, agent_id: int) -> bool:
        """Delete agent"""
        agent = await self.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        await self.db.delete(agent)
        await self.db.commit()
        return True
    
    async def update_status(self, agent_id: int, status: AgentStatus) -> Agent:
        """Update agent status"""
        return await self.update(agent_id, {"status": status})
    
    async def reset_status(self, agent_id: int) -> Agent:
        """Reset agent status to idle"""
        return await self.update_status(agent_id, AgentStatus.IDLE)
    
    async def increment_execution_stats(
        self,
        agent_id: int,
        success: bool,
        execution_time: float
    ) -> Agent:
        """Update agent execution statistics"""
        agent = await self.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        agent.increment_execution_stats(success, execution_time)
        agent.last_executed_at = func.now()
        
        await self.db.commit()
        await self.db.refresh(agent)
        return agent
    
    async def search_agents(
        self,
        search_term: str,
        owner_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Agent]:
        """Search agents by name or description"""
        search_pattern = f"%{search_term}%"
        
        query = select(Agent).where(
            (Agent.name.ilike(search_pattern)) |
            (Agent.description.ilike(search_pattern))
        )
        
        if owner_id:
            query = query.where(Agent.owner_id == owner_id)
        else:
            query = query.where(Agent.is_public == True)
        
        query = query.where(Agent.is_active == True)
        query = query.offset(skip).limit(limit).order_by(Agent.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_agent_stats(self, agent_id: int) -> Dict[str, Any]:
        """Get agent statistics"""
        agent = await self.get_by_id(agent_id)
        if not agent:
            raise NotFoundError(f"Agent with ID {agent_id} not found")
        
        # Get related counts
        from app.models.task import Task
        from app.models.conversation import Conversation
        
        tasks_count = await self.db.scalar(
            select(func.count(Task.id)).where(Task.agent_id == agent_id)
        )
        
        conversations_count = await self.db.scalar(
            select(func.count(Conversation.id)).where(Conversation.agent_id == agent_id)
        )
        
        return {
            "agent": agent.to_dict(),
            "stats": {
                "tasks_count": tasks_count or 0,
                "conversations_count": conversations_count or 0,
                "success_rate": agent.success_rate,
                "total_executions": agent.total_executions,
                "average_execution_time": agent.average_execution_time,
                "uptime_days": (datetime.utcnow() - agent.created_at).days if agent.created_at else 0
            }
        }
