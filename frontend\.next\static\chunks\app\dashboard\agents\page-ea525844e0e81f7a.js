(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[638],{7589:function(e,s,t){Promise.resolve().then(t.bind(t,4447))},4447:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return g}});var r=t(7437),a=t(2265),n=t(1713),l=t(7648),i=t(3836);let d=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var c=t(1331);let o=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"}))});var u=t(5836),m=t(7692),x=t(1244),h=t(3448);function g(){let[e,s]=(0,a.useState)(""),[t,g]=(0,a.useState)(""),{data:f,isLoading:p,error:b}=(0,n.a)({queryKey:["agents",{search:e,type:t}],queryFn:()=>x.G.getAgents({agent_type:t||void 0})}),{data:j}=(0,n.a)({queryKey:["agent-types"],queryFn:()=>x.G.getAgentTypes()}),y=(null==f?void 0:f.filter(s=>{var t;return s.name.toLowerCase().includes(e.toLowerCase())||(null===(t=s.description)||void 0===t?void 0:t.toLowerCase().includes(e.toLowerCase()))}))||[],v=e=>{switch(e){case"idle":return"bg-green-100 text-green-800";case"running":return"bg-blue-100 text-blue-800";case"error":return"bg-red-100 text-red-800";case"paused":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(0,r.jsx)(u.c,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Agents"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your AI agents and their configurations"})]}),(0,r.jsxs)(l.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(i.Z,{className:"h-4 w-4 mr-2"}),"Create Agent"]})]}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(d,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search agents...",value:e,onChange:e=>s(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:t,onChange:e=>g(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Types"}),null==j?void 0:j.types.map(e=>(0,r.jsx)("option",{value:e.value,children:e.name},e.value))]})]})}),p?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(m.T,{size:"lg"})}):b?(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-red-600",children:"Error loading agents"})}):0===y.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(c.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No agents"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by creating your first agent."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)(l.default,{href:"/dashboard/agents/new",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(i.Z,{className:"h-4 w-4 mr-2"}),"Create Agent"]})})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:y.map(e=>(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.Z,{className:"h-8 w-8 text-blue-500"})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.agent_type.replace("_"," ")})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(v(e.status)),children:e.status}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(o,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description||"No description provided"})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[e.total_executions," executions"]}),(0,r.jsx)("span",{children:(0,h.SY)(e.updated_at)})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(l.default,{href:"/dashboard/agents/".concat(e.id),className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"View Details"})})]})},e.id))})]})})}}},function(e){e.O(0,[64,565,763,140,291,971,117,744],function(){return e(e.s=7589)}),_N_E=e.O()}]);