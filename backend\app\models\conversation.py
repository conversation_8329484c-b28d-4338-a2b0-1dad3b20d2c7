"""
Conversation model
"""

from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateTime, Text, JSON, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class ConversationStatus(str, enum.Enum):
    """Conversation status enumeration"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class MessageRole(str, enum.Enum):
    """Message role enumeration"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class Conversation(Base):
    """Conversation model"""
    
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Status
    status = Column(Enum(ConversationStatus), default=ConversationStatus.ACTIVE, nullable=False)
    is_archived = Column(<PERSON><PERSON><PERSON>, default=False, nullable=False)
    
    # Configuration
    context = Column(JSON, default=dict, nullable=True)  # Conversation context
    meta_data = Column(JSON, default=dict, nullable=True)  # Additional metadata
    
    # Statistics
    message_count = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_message_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="conversations")
    agent = relationship("Agent", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan", order_by="Message.created_at")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    def add_message(self, role: MessageRole, content: str, meta_data: dict = None) -> "Message":
        """Add a message to the conversation"""
        message = Message(
            conversation_id=self.id,
            role=role,
            content=content,
            meta_data=meta_data or {}
        )
        self.message_count += 1
        self.last_message_at = func.now()
        return message
    
    def get_messages_for_llm(self, limit: int = None) -> list:
        """Get messages formatted for LLM"""
        messages = self.messages
        if limit:
            messages = messages[-limit:]
        
        return [
            {
                "role": msg.role.value,
                "content": msg.content,
                "timestamp": msg.created_at.isoformat() if msg.created_at else None
            }
            for msg in messages
        ]
    
    def to_dict(self) -> dict:
        """Convert conversation to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "status": self.status.value if self.status else None,
            "is_archived": self.is_archived,
            "context": self.context,
            "metadata": self.meta_data,
            "message_count": self.message_count,
            "total_tokens": self.total_tokens,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_message_at": self.last_message_at.isoformat() if self.last_message_at else None,
            "user_id": self.user_id,
            "agent_id": self.agent_id,
        }


class Message(Base):
    """Message model"""
    
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    role = Column(Enum(MessageRole), nullable=False)
    content = Column(Text, nullable=False)
    
    # Message metadata
    meta_data = Column(JSON, default=dict, nullable=True)
    tokens = Column(Integer, default=0, nullable=False)
    
    # Tool usage
    tool_calls = Column(JSON, default=list, nullable=True)
    tool_results = Column(JSON, default=list, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Foreign keys
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, role='{self.role}', conversation_id={self.conversation_id})>"
    
    def to_dict(self) -> dict:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "role": self.role.value if self.role else None,
            "content": self.content,
            "metadata": self.meta_data,
            "tokens": self.tokens,
            "tool_calls": self.tool_calls,
            "tool_results": self.tool_results,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "conversation_id": self.conversation_id,
        }
