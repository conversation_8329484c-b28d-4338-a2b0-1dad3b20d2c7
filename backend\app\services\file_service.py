"""
File service for database operations and file management
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func
from datetime import datetime, timedelta

from app.models.file import File, FileStatus, FileType
from app.core.exceptions import NotFoundError, ValidationError


class FileService:
    """File service for database operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, file_data: Dict[str, Any]) -> File:
        """Create a new file record"""
        file_obj = File(**file_data)
        self.db.add(file_obj)
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def get_by_id(self, file_id: int) -> Optional[File]:
        """Get file by ID"""
        result = await self.db.execute(
            select(File).where(File.id == file_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_owner(
        self,
        owner_id: int,
        skip: int = 0,
        limit: int = 100,
        file_type: Optional[FileType] = None,
        status: Optional[FileStatus] = None
    ) -> List[File]:
        """Get files by owner"""
        query = select(File).where(File.owner_id == owner_id)
        
        if file_type:
            query = query.where(File.file_type == file_type)
        
        if status:
            query = query.where(File.status == status)
        
        query = query.offset(skip).limit(limit).order_by(File.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_hash(self, file_hash: str) -> Optional[File]:
        """Get file by hash (for deduplication)"""
        result = await self.db.execute(
            select(File).where(File.file_hash == file_hash)
        )
        return result.scalar_one_or_none()
    
    async def update(self, file_id: int, file_data: Dict[str, Any]) -> File:
        """Update file"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        # Update fields
        for field, value in file_data.items():
            if hasattr(file_obj, field):
                setattr(file_obj, field, value)
        
        file_obj.updated_at = func.now()
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def delete(self, file_id: int) -> bool:
        """Delete file record"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        await self.db.delete(file_obj)
        await self.db.commit()
        return True
    
    async def update_status(self, file_id: int, status: FileStatus) -> File:
        """Update file status"""
        return await self.update(file_id, {"status": status})
    
    async def increment_download_count(self, file_id: int) -> File:
        """Increment download count"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        file_obj.increment_download_count()
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def increment_view_count(self, file_id: int) -> File:
        """Increment view count"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        file_obj.increment_view_count()
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def share_with_user(self, file_id: int, user_id: int) -> File:
        """Share file with a user"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        file_obj.share_with_user(user_id)
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def unshare_with_user(self, file_id: int, user_id: int) -> File:
        """Unshare file with a user"""
        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        file_obj.unshare_with_user(user_id)
        await self.db.commit()
        await self.db.refresh(file_obj)
        return file_obj
    
    async def get_expired_files(self) -> List[File]:
        """Get expired files"""
        now = datetime.utcnow()
        query = select(File).where(
            File.expires_at.isnot(None),
            File.expires_at < now,
            File.auto_delete == True
        )
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_expired_temporary_files(self, hours: int = 24) -> List[File]:
        """Get expired temporary files"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        query = select(File).where(
            File.is_temporary == True,
            File.created_at < cutoff_time
        )
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def search_files(
        self,
        search_term: str,
        owner_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[File]:
        """Search files by filename or content"""
        search_pattern = f"%{search_term}%"
        
        query = select(File).where(
            (File.filename.ilike(search_pattern)) |
            (File.original_filename.ilike(search_pattern)) |
            (File.extracted_text.ilike(search_pattern))
        )
        
        if owner_id:
            query = query.where(File.owner_id == owner_id)
        else:
            query = query.where(File.is_public == True)
        
        query = query.where(File.status == FileStatus.READY)
        query = query.offset(skip).limit(limit).order_by(File.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_file_stats(self, owner_id: Optional[int] = None) -> Dict[str, Any]:
        """Get file statistics"""
        base_query = select(func.count(File.id))
        
        if owner_id:
            base_query = base_query.where(File.owner_id == owner_id)
        
        # Get counts by status
        stats = {}
        for status in FileStatus:
            query = base_query.where(File.status == status)
            count = await self.db.scalar(query)
            stats[f"{status.value}_count"] = count or 0
        
        # Get counts by type
        for file_type in FileType:
            query = base_query.where(File.file_type == file_type)
            count = await self.db.scalar(query)
            stats[f"{file_type.value}_count"] = count or 0
        
        # Get total size
        size_query = select(func.sum(File.file_size))
        if owner_id:
            size_query = size_query.where(File.owner_id == owner_id)
        
        total_size = await self.db.scalar(size_query)
        stats["total_size_bytes"] = total_size or 0
        stats["total_size_mb"] = round((total_size or 0) / (1024 * 1024), 2)
        
        # Get total count
        total_count = await self.db.scalar(base_query)
        stats["total_count"] = total_count or 0
        
        return stats
    
    async def delete_from_storage(self, file_id: int) -> bool:
        """Delete file from storage"""
        from app.services.local_storage_service import local_storage

        file_obj = await self.get_by_id(file_id)
        if not file_obj:
            return False

        # Delete from local storage
        if hasattr(file_obj, 'storage_path') and file_obj.storage_path:
            return local_storage.delete_file(file_obj.storage_path)

        return False
