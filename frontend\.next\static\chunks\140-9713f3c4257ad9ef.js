"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[140],{4355:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=i},3767:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});t.Z=i},9152:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=i},1331:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))});t.Z=i},7626:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});t.Z=i},9778:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});t.Z=i},3836:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});t.Z=i},338:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=i},7165:function(e,t,r){var s=r(2265);let i=s.forwardRef(function(e,t){let{title:r,titleId:i,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},n),r?s.createElement("title",{id:i},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=i},1713:function(e,t,r){let s;r.d(t,{a:function(){return I}});var i=r(7045),n=r(8238),a=r(1733),o=r(4112),l=r(6803),u=r(5345),c=class extends o.l{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,l.O)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#i=void 0;#n=void 0;#a;#o;#r;#t;#l;#u;#c;#h;#d;#f;#p=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),h(this.#s,this.options)?this.#v():this.updateResult(),this.#m())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#y(),this.#R(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Nc)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#b(),this.#s.setOptions(this.options),t._defaulted&&!(0,u.VS)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let s=this.hasListeners();s&&f(this.#s,r,this.options,t)&&this.#v(),this.updateResult(),s&&(this.#s!==r||(0,u.Nc)(this.options.enabled,this.#s)!==(0,u.Nc)(t.enabled,this.#s)||(0,u.KC)(this.options.staleTime,this.#s)!==(0,u.KC)(t.staleTime,this.#s))&&this.#g();let i=this.#w();s&&(this.#s!==r||(0,u.Nc)(this.options.enabled,this.#s)!==(0,u.Nc)(t.enabled,this.#s)||i!==this.#f)&&this.#k(i)}getOptimisticResult(e){let t=this.#e.getQueryCache().build(this.#e,e),r=this.createResult(t,e);return(0,u.VS)(this.getCurrentResult(),r)||(this.#n=r,this.#o=this.options,this.#a=this.#s.state),r}getCurrentResult(){return this.#n}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#p.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#v({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#v(e){this.#b();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(u.ZT)),t}#g(){this.#y();let e=(0,u.KC)(this.options.staleTime,this.#s);if(u.sk||this.#n.isStale||!(0,u.PN)(e))return;let t=(0,u.Kp)(this.#n.dataUpdatedAt,e);this.#h=setTimeout(()=>{this.#n.isStale||this.updateResult()},t+1)}#w(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#k(e){this.#R(),this.#f=e,!u.sk&&!1!==(0,u.Nc)(this.options.enabled,this.#s)&&(0,u.PN)(this.#f)&&0!==this.#f&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||i.j.isFocused())&&this.#v()},this.#f))}#m(){this.#g(),this.#k(this.#w())}#y(){this.#h&&(clearTimeout(this.#h),this.#h=void 0)}#R(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(e,t){let r;let s=this.#s,i=this.options,n=this.#n,o=this.#a,c=this.#o,d=e!==s?e.state:this.#i,{state:v}=e,m={...v},y=!1;if(t._optimisticResults){let r=this.hasListeners(),n=!r&&h(e,t),o=r&&f(e,s,t,i);(n||o)&&(m={...m,...(0,a.z)(v.data,e.options)}),"isRestoring"===t._optimisticResults&&(m.fetchStatus="idle")}let{error:R,errorUpdatedAt:b,status:g}=m;r=m.data;let w=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===g){let e;n?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=n.data,w=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#c?.state.data,this.#c):t.placeholderData,void 0!==e&&(g="success",r=(0,u.oE)(n?.data,e,t),y=!0)}if(t.select&&void 0!==r&&!w){if(n&&r===o?.data&&t.select===this.#l)r=this.#u;else try{this.#l=t.select,r=t.select(r),r=(0,u.oE)(n?.data,r,t),this.#u=r,this.#t=null}catch(e){this.#t=e}}this.#t&&(R=this.#t,r=this.#u,b=Date.now(),g="error");let k="fetching"===m.fetchStatus,Q="pending"===g,E="error"===g,O=Q&&k,C=void 0!==r,x={status:g,fetchStatus:m.fetchStatus,isPending:Q,isSuccess:"success"===g,isError:E,isInitialLoading:O,isLoading:O,data:r,dataUpdatedAt:m.dataUpdatedAt,error:R,errorUpdatedAt:b,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>d.dataUpdateCount||m.errorUpdateCount>d.errorUpdateCount,isFetching:k,isRefetching:k&&!Q,isLoadingError:E&&!C,isPaused:"paused"===m.fetchStatus,isPlaceholderData:y,isRefetchError:E&&C,isStale:p(e,t),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===x.status?e.reject(x.error):void 0!==x.data&&e.resolve(x.data)},r=()=>{t(this.#r=x.promise=(0,l.O)())},i=this.#r;switch(i.status){case"pending":e.queryHash===s.queryHash&&t(i);break;case"fulfilled":("error"===x.status||x.data!==i.value)&&r();break;case"rejected":("error"!==x.status||x.error!==i.reason)&&r()}}return x}updateResult(){let e=this.#n,t=this.createResult(this.#s,this.options);this.#a=this.#s.state,this.#o=this.options,void 0!==this.#a.data&&(this.#c=this.#s),(0,u.VS)(t,e)||(this.#n=t,this.#Q({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#p.size)return!0;let s=new Set(r??this.#p);return this.options.throwOnError&&s.add("error"),Object.keys(this.#n).some(t=>this.#n[t]!==e[t]&&s.has(t))})()}))}#b(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#m()}#Q(e){n.Vr.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#n)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function h(e,t){return!1!==(0,u.Nc)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,u.Nc)(t.enabled,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function f(e,t,r,s){return(e!==t||!1===(0,u.Nc)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,u.Nc)(t.enabled,e)&&e.isStaleByTime((0,u.KC)(t.staleTime,e))}var v=r(2265),m=r(9827);r(7437);var y=v.createContext((s=!1,{clearReset:()=>{s=!1},reset:()=>{s=!0},isReset:()=>s})),R=()=>v.useContext(y),b=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},g=e=>{v.useEffect(()=>{e.clearReset()},[e])},w=e=>{let{result:t,errorResetBoundary:r,throwOnError:s,query:i,suspense:n}=e;return t.isError&&!r.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||(0,u.L3)(s,[t.error,i]))},k=v.createContext(!1),Q=()=>v.useContext(k);k.Provider;var E=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},O=(e,t)=>e.isLoading&&e.isFetching&&!t,C=(e,t)=>e?.suspense&&t.isPending,x=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function I(e,t){return function(e,t,r){var s,i,a,o,l;let c=(0,m.NL)(r),h=Q(),d=R(),f=c.defaultQueryOptions(e);null===(i=c.getDefaultOptions().queries)||void 0===i||null===(s=i._experimental_beforeQuery)||void 0===s||s.call(i,f),f._optimisticResults=h?"isRestoring":"optimistic",E(f),b(f,d),g(d);let p=!c.getQueryCache().get(f.queryHash),[y]=v.useState(()=>new t(c,f)),k=y.getOptimisticResult(f),I=!h&&!1!==e.subscribed;if(v.useSyncExternalStore(v.useCallback(e=>{let t=I?y.subscribe(n.Vr.batchCalls(e)):u.ZT;return y.updateResult(),t},[y,I]),()=>y.getCurrentResult(),()=>y.getCurrentResult()),v.useEffect(()=>{y.setOptions(f)},[f,y]),C(f,k))throw x(f,y,d);if(w({result:k,errorResetBoundary:d,throwOnError:f.throwOnError,query:c.getQueryCache().get(f.queryHash),suspense:f.suspense}))throw k.error;if(null===(o=c.getDefaultOptions().queries)||void 0===o||null===(a=o._experimental_afterQuery)||void 0===a||a.call(o,f,k),f.experimental_prefetchInRender&&!u.sk&&O(k,h)){let e=p?x(f,y,d):null===(l=c.getQueryCache().get(f.queryHash))||void 0===l?void 0:l.promise;null==e||e.catch(u.ZT).finally(()=>{y.updateResult()})}return f.notifyOnChangeProps?k:y.trackResult(k)}(e,c,t)}}}]);