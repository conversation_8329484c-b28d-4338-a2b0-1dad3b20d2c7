'use client';

import { useQuery } from '@tanstack/react-query';
import {
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  DocumentIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { agentService } from '@/services/agent-service';
import { conversationService } from '@/services/conversation-service';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { StatsCard } from '@/components/ui/stats-card';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { QuickActions } from '@/components/dashboard/quick-actions';

export function DashboardOverview() {
  const { data: agents, isLoading: agentsLoading } = useQuery({
    queryKey: ['agents'],
    queryFn: () => agentService.getAgents(),
  });

  const { data: conversations, isLoading: conversationsLoading } = useQuery({
    queryKey: ['conversations'],
    queryFn: () => conversationService.getConversations(),
  });

  const isLoading = agentsLoading || conversationsLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const stats = [
    {
      name: 'Active Agents',
      value: agents?.filter((agent: any) => agent.is_active).length || 0,
      icon: CpuChipIcon,
      color: 'blue' as const,
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      name: 'Conversations',
      value: conversations?.length || 0,
      icon: ChatBubbleLeftRightIcon,
      color: 'green' as const,
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      name: 'Files Processed',
      value: 24,
      icon: DocumentIcon,
      color: 'purple' as const,
      change: '+18%',
      changeType: 'positive' as const,
    },
    {
      name: 'Avg Response Time',
      value: '2.3s',
      icon: ClockIcon,
      color: 'orange' as const,
      change: '-8%',
      changeType: 'positive' as const,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your AI agents.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatsCard key={stat.name} {...stat} />
        ))}
      </div>

      {/* Quick Actions */}
      <QuickActions />

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentActivity />
        
        {/* Agent Performance Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Agent Performance
          </h3>
          <div className="h-64 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <CpuChipIcon className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>Performance charts coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
