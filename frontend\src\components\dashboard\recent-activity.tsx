'use client';

import { useQuery } from '@tanstack/react-query';
import {
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';
import { formatRelativeTime } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

// Mock data for now - replace with actual API calls
const mockActivities = [
  {
    id: 1,
    type: 'agent_created',
    title: 'Created new Code Agent',
    description: 'Python development assistant',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    icon: CpuChipIcon,
    iconColor: 'text-blue-500',
    status: 'success',
  },
  {
    id: 2,
    type: 'conversation_started',
    title: 'Started conversation with Research Agent',
    description: 'Market analysis discussion',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    icon: ChatBubbleLeftRightIcon,
    iconColor: 'text-green-500',
    status: 'success',
  },
  {
    id: 3,
    type: 'task_completed',
    title: 'Task completed successfully',
    description: 'Data analysis report generated',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
    icon: CheckCircleIcon,
    iconColor: 'text-green-500',
    status: 'success',
  },
  {
    id: 4,
    type: 'task_failed',
    title: 'Task execution failed',
    description: 'API connection timeout',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
    icon: ExclamationCircleIcon,
    iconColor: 'text-red-500',
    status: 'error',
  },
];

export function RecentActivity() {
  // For now, use mock data. Replace with actual API call
  const { data: activities = mockActivities, isLoading } = useQuery({
    queryKey: ['recent-activity'],
    queryFn: async () => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return mockActivities;
    },
  });

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Recent Activity
      </h3>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="flow-root">
          <ul className="-mb-8">
            {activities.map((activity, activityIdx) => (
              <li key={activity.id}>
                <div className="relative pb-8">
                  {activityIdx !== activities.length - 1 ? (
                    <span
                      className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3">
                    <div>
                      <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                        activity.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                      }`}>
                        <activity.icon className={`h-5 w-5 text-white`} />
                      </span>
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm text-gray-900 font-medium">
                          {activity.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {activity.description}
                        </p>
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500">
                        {formatRelativeTime(activity.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {!isLoading && activities.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No recent activity</p>
        </div>
      )}
    </div>
  );
}
